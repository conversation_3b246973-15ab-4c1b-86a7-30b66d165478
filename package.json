{"name": "mindify-aid", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "postinstall": "prisma generate", "vercel-build": "prisma generate && next build", "build": "next build", "start": "next start", "lint": "eslint --fix", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "type-check": "tsc --noEmit", "prepare": "husky"}, "dependencies": {"@heroui/avatar": "^2.2.17", "@heroui/button": "2.2.21", "@heroui/card": "^2.2.20", "@heroui/checkbox": "2.3.20", "@heroui/chip": "2.2.17", "@heroui/code": "2.2.16", "@heroui/divider": "^2.2.15", "@heroui/dropdown": "2.3.21", "@heroui/image": "^2.2.13", "@heroui/input": "^2.4.21", "@heroui/kbd": "2.2.17", "@heroui/link": "2.2.18", "@heroui/listbox": "2.3.20", "@heroui/modal": "^2.2.18", "@heroui/navbar": "2.2.19", "@heroui/progress": "^2.2.17", "@heroui/select": "^2.4.21", "@heroui/snippet": "2.2.22", "@heroui/spacer": "^2.2.16", "@heroui/switch": "2.2.19", "@heroui/system": "2.4.17", "@heroui/table": "^2.2.20", "@heroui/theme": "2.4.17", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@react-aria/ssr": "3.9.9", "@react-aria/visually-hidden": "3.8.24", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/typography": "^0.5.16", "@types/airtable": "^0.10.5", "@types/bcryptjs": "^3.0.0", "@types/nodemailer": "^6.4.17", "@types/stripe": "^8.0.416", "@vercel/analytics": "^1.5.0", "airtable": "^0.12.2", "bcryptjs": "^3.0.2", "clsx": "2.1.1", "framer-motion": "11.13.1", "gray-matter": "^4.0.3", "highlight.js": "^11.11.1", "intl-messageformat": "10.7.16", "lucide-react": "^0.516.0", "nanoid": "^5.1.5", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "0.4.6", "nodemailer": "^6.10.1", "prisma": "^6.9.0", "react": "18.3.1", "react-dom": "18.3.1", "recharts": "^2.15.3", "rehype-highlight": "^7.0.2", "rehype-stringify": "^10.0.1", "remark-mdx": "^3.1.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "resend": "^4.6.0", "stripe": "^18.2.1", "unified": "^11.0.5", "zod": "^3.25.64"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@next/eslint-plugin-next": "15.3.1", "@react-types/shared": "3.30.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "autoprefixer": "10.4.21", "eslint": "^9.29.0", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "msw": "^2.10.2", "postcss": "8.5.3", "prettier": "^3.1.0", "prettier-eslint": "^16.1.2", "supabase": "^2.24.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "prisma": {"schema": "prisma/schema.prisma"}}