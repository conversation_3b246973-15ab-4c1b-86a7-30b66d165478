export function stringifyDates<T>(input: T): T {
  if (input instanceof Date) {
    return input.toISOString() as any;
  }

  if (Array.isArray(input)) {
    return input.map((item) => stringifyDates(item)) as any;
  }

  if (input !== null && typeof input === "object") {
    const result: any = {};

    for (const key of Object.keys(input)) {
      result[key] = stringifyDates((input as any)[key]);
    }

    return result;
  }

  // primitive, null, undefined – return as-is
  return input;
}
