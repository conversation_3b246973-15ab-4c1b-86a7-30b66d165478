import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";
import { Prisma } from "@prisma/client";

import { validateData } from "@/lib/validation";
import { APIError } from "@/lib/errors";

export function handleAPIError(error: unknown): NextResponse {
  console.error("Service error:", error);

  // Handle APIError instances (includes all custom error classes)
  if (error instanceof APIError) {
    return NextResponse.json(
      { error: error.message },
      { status: error.status },
    );
  }

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case "P2002": // Unique constraint violation
        return NextResponse.json(
          { error: error.message || "Unique constraint violation" },
          { status: 409 },
        );
      case "P2003": // Foreign key constraint violation
        return NextResponse.json(
          { error: error.message || "Foreign key constraint violation" },
          { status: 400 },
        );
      case "P2025": // Record not found
        return NextResponse.json(
          { error: error.message || "Record not found" },
          { status: 404 },
        );
      default:
        return NextResponse.json(
          {
            error: "Database operation failed with code: " + error.code,
          },
          { status: 500 },
        );
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  // Fallback for unknown errors
  return NextResponse.json({ error: "Internal server error" }, { status: 500 });
}

export function withAPIErrorHandling(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
): (request: NextRequest, context?: any) => Promise<NextResponse> {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleAPIError(error);
    }
  };
}

export function withValidationAPIErrorHandling<T>(
  handler: (
    request: NextRequest,
    validatedData: T,
    context?: any,
  ) => Promise<NextResponse>,
  schema?: z.ZodSchema<T>,
): (request: NextRequest, context?: any) => Promise<NextResponse> {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      let body: unknown = {};

      const hasBody =
        request.method === "POST" ||
        request.method === "PUT" ||
        request.method === "PATCH";

      if (hasBody) {
        try {
          body = await request.json();
        } catch {
          return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
        }
      }

      if (schema) {
        const validation = validateData(schema, body);

        if (!validation.success) {
          return NextResponse.json(
            { error: validation.error },
            { status: 400 },
          );
        }

        return await handler(request, validation.data, context);
      }

      return await handler(request, undefined as T, context);
    } catch (error) {
      return handleAPIError(error);
    }
  };
}
