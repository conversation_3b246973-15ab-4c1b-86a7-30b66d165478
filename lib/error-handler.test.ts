import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";
import { Prisma } from "@prisma/client";

import {
  withValidationAPIErrorHandling,
  handleAPIError,
} from "./error-handler";
// eslint-disable-next-line import/order
import {
  APIError,
  UserNotFoundError,
  InvalidCredentialsError,
  EmailAlreadyVerifiedError,
} from "./errors";

// Mock validateData function
vi.mock("./validation", () => ({
  validateData: vi.fn(),
}));

import { validateData } from "./validation";

describe("Error Handler System", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    console.error = vi.fn(); // Mock console.error to avoid noise in tests
  });

  describe("APIError base class", () => {
    it("should create APIError with default status 500", () => {
      const error = new APIError("Test error");

      expect(error.message).toBe("Test error");
      expect(error.status).toBe(500);
      expect(error.name).toBe("APIError");
    });

    it("should create APIError with custom status", () => {
      const error = new APIError("Custom error", 400);

      expect(error.message).toBe("Custom error");
      expect(error.status).toBe(400);
    });
  });

  describe("Custom error classes", () => {
    it("should create UserNotFoundError with correct status", () => {
      const error = new UserNotFoundError();

      expect(error.message).toBe("User not found");
      expect(error.status).toBe(404);
      expect(error.name).toBe("UserNotFoundError");
      expect(error instanceof APIError).toBe(true);
    });

    it("should create InvalidCredentialsError with correct status", () => {
      const error = new InvalidCredentialsError();

      expect(error.message).toBe("Invalid credentials");
      expect(error.status).toBe(401);
      expect(error.name).toBe("InvalidCredentialsError");
    });

    it("should create EmailAlreadyVerifiedError with correct status", () => {
      const error = new EmailAlreadyVerifiedError();

      expect(error.message).toBe("Email is already verified");
      expect(error.status).toBe(409);
      expect(error.name).toBe("EmailAlreadyVerifiedError");
    });
  });

  describe("ApiErrorHandler.handleServiceError", () => {
    it("should handle APIError instances with their status codes", () => {
      const error = new UserNotFoundError("Custom user not found message");
      const response = handleAPIError(error);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(404);
    });

    it("should handle generic Error instances with 500 status", () => {
      const error = new Error("Generic error");
      const response = handleAPIError(error);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(500);
    });

    it("should handle Prisma P2002 error (unique constraint)", () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Unique constraint failed on the fields: (`email`)",
        {
          code: "P2002",
          clientVersion: "4.0.0",
        },
      );
      const response = handleAPIError(prismaError);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(409);
    });

    it("should handle Prisma P2025 error (record not found)", () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Record to update not found.",
        {
          code: "P2025",
          clientVersion: "4.0.0",
        },
      );
      const response = handleAPIError(prismaError);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(404);
    });

    it("should handle unknown Prisma errors with 500 status", () => {
      const prismaError = {
        code: "P9999",
        message: "Unknown Prisma error",
      };
      const response = handleAPIError(prismaError);

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(500);
    });

    it("should handle unknown errors with 500 status", () => {
      const response = handleAPIError("string error");

      expect(response).toBeInstanceOf(NextResponse);
      expect(response.status).toBe(500);
    });
  });

  describe("withApiErrorHandling (enhanced)", () => {
    const testSchema = z.object({
      name: z.string().min(1, "Name is required"),
      email: z.email("Invalid email"),
    });

    it("should validate request data and call handler with validated data", async () => {
      const mockHandler = vi
        .fn()
        .mockResolvedValue(NextResponse.json({ success: true }));

      vi.mocked(validateData).mockReturnValue({
        success: true,
        data: { name: "John", email: "<EMAIL>" },
      });

      const wrappedHandler = withValidationAPIErrorHandling(
        mockHandler,
        testSchema,
      );
      const request = new NextRequest("http://localhost/test", {
        method: "POST",
        body: JSON.stringify({ name: "John", email: "<EMAIL>" }),
      });

      const response = await wrappedHandler(request);

      expect(validateData).toHaveBeenCalledWith(testSchema, {
        name: "John",
        email: "<EMAIL>",
      });
      expect(mockHandler).toHaveBeenCalledWith(
        request,
        {
          name: "John",
          email: "<EMAIL>",
        },
        undefined,
      );
      expect(response.status).toBe(200);
    });

    it("should return 400 for validation errors", async () => {
      const mockHandler = vi.fn();

      vi.mocked(validateData).mockReturnValue({
        success: false,
        error: "Name is required",
      });

      const wrappedHandler = withValidationAPIErrorHandling(
        mockHandler,
        testSchema,
      );
      const request = new NextRequest("http://localhost/test", {
        method: "POST",
        body: JSON.stringify({ email: "<EMAIL>" }),
      });

      const response = await wrappedHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Name is required");
      expect(mockHandler).not.toHaveBeenCalled();
    });

    it("should handle service errors thrown by handler", async () => {
      const mockHandler = vi
        .fn()
        .mockRejectedValue(new UserNotFoundError("User does not exist"));

      vi.mocked(validateData).mockReturnValue({
        success: true,
        data: { name: "John", email: "<EMAIL>" },
      });

      const wrappedHandler = withValidationAPIErrorHandling(
        mockHandler,
        testSchema,
      );
      const request = new NextRequest("http://localhost/test", {
        method: "POST",
        body: JSON.stringify({ name: "John", email: "<EMAIL>" }),
      });

      const response = await wrappedHandler(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("User does not exist");
    });

    it("should handle JSON parsing errors", async () => {
      const mockHandler = vi.fn();
      const wrappedHandler = withValidationAPIErrorHandling(
        mockHandler,
        testSchema,
      );
      const request = new NextRequest("http://localhost/test", {
        method: "POST",
        body: "invalid json",
      });

      const response = await wrappedHandler(request);

      expect(response.status).toBe(400);
      expect(mockHandler).not.toHaveBeenCalled();
    });
  });

  describe("withApiErrorHandlingLegacy (backward compatibility)", () => {
    it("should handle errors thrown by handler", async () => {
      const mockHandler = vi
        .fn()
        .mockRejectedValue(new InvalidCredentialsError("Bad credentials"));

      const wrappedHandler = withValidationAPIErrorHandling(mockHandler);
      const request = new NextRequest("http://localhost/test");

      const response = await wrappedHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Bad credentials");
    });
  });
});
