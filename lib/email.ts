import crypto from "crypto";

import { Resend } from "resend";
import nodemailer from "nodemailer";

import { EmailSendError } from "@/lib/errors";

// Initialize Resend
export const resend = new Resend(process.env.RESEND_API_KEY);

// Gmail transporter as fallback
const gmailTransporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.GMAIL_USER,
    pass: process.env.GMAIL_PASS,
  },
});

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
}

export type EmailResult = "resend" | "gmail";

/**
 * Generate a secure verification token
 */
export function generateVerificationToken(): string {
  return crypto.randomBytes(32).toString("hex");
}

/**
 * Get verification token expiry date (24 hours from now)
 */
export function getVerificationExpiry(): Date {
  return new Date(Date.now() + 24 * 60 * 60 * 1000);
}

/**
 * Generic email sending function that handles both Resend and Gmail fallback
 * Throws EmailSendError on failure instead of returning error objects
 */
export async function sendEmail(emailData: EmailData): Promise<EmailResult> {
  const fromEmail =
    emailData.from || process.env.EMAIL_FROM || "<EMAIL>";

  // Try Resend first
  if (
    process.env.RESEND_API_KEY &&
    process.env.RESEND_API_KEY !== "your_resend_api_key_here"
  ) {
    try {
      const result = await resend.emails.send({
        from: fromEmail,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      });

      if (!result.error) {
        console.log(`Email sent to ${emailData.to} via Resend`);

        return "resend";
      }
      console.error("Resend failed:", result.error);
      console.log("Trying Gmail fallback...");
    } catch (error) {
      console.error("Resend error:", error);
      console.log("Trying Gmail fallback...");
    }
  }

  // Fallback to Gmail
  if (
    process.env.GMAIL_USER &&
    process.env.GMAIL_PASS &&
    process.env.GMAIL_USER !== "<EMAIL>" &&
    process.env.GMAIL_PASS !== "your_gmail_app_password_here"
  ) {
    try {
      await gmailTransporter.sendMail({
        from: fromEmail,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      });

      console.log(`Email sent to ${emailData.to} via Gmail`);

      return "gmail";
    } catch (error) {
      console.error("Gmail error:", error);
      throw new EmailSendError(
        error instanceof Error
          ? error.message
          : "Failed to send email via Gmail",
      );
    }
  }

  throw new EmailSendError("No email service configured");
}
