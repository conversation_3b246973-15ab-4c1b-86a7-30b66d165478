import { prisma } from "@/lib/db";

/**
 * Analytics utility functions for calculating real metrics from database data
 */

export interface AnalyticsData {
  impressions: number;
  clicks: number;
  revenue?: number;
  spend?: number;
  ctr: number;
}

export interface MonthlyData {
  month: string;
  impressions: number;
  clicks: number;
  revenue?: number;
  spend?: number;
}

/**
 * Calculate analytics for a specific advertisement
 */
export async function getAdAnalytics(adId: string): Promise<AnalyticsData> {
  const impressions = await prisma.adImpression.findMany({
    where: { adId },
    select: {
      clicked: true,
      advertisement: {
        select: {
          bidType: true,
          bidAmount: true,
        },
      },
    },
  });

  const totalImpressions = impressions.length;
  const totalClicks = impressions.filter((imp) => imp.clicked).length;
  const ctr = totalImpressions > 0 ? totalClicks / totalImpressions : 0;

  // Calculate spend based on bid type
  let spend = 0;

  if (impressions.length > 0) {
    const { bidType, bidAmount } = impressions[0].advertisement;

    if (bidType === "CPC") {
      spend = totalClicks * Number(bidAmount);
    } else if (bidType === "CPM") {
      spend = (totalImpressions / 1000) * Number(bidAmount);
    }
  }

  return {
    impressions: totalImpressions,
    clicks: totalClicks,
    spend,
    ctr,
  };
}

/**
 * Calculate analytics for multiple advertisements belonging to a user
 */
export async function getUserAdAnalytics(userId: string): Promise<{
  totalImpressions: number;
  totalClicks: number;
  totalSpend: number;
  averageCTR: number;
  ads: Array<{ id: string; name: string } & AnalyticsData>;
}> {
  const userAds = await prisma.advertisement.findMany({
    where: { userId },
    select: {
      id: true,
      name: true,
      impressions: {
        select: {
          clicked: true,
          advertisement: {
            select: {
              bidType: true,
              bidAmount: true,
            },
          },
        },
      },
    },
  });

  const adsWithAnalytics = await Promise.all(
    userAds.map(async (ad) => {
      const analytics = await getAdAnalytics(ad.id);

      return {
        id: ad.id,
        name: ad.name,
        ...analytics,
      };
    }),
  );

  const totalImpressions = adsWithAnalytics.reduce(
    (sum, ad) => sum + ad.impressions,
    0,
  );
  const totalClicks = adsWithAnalytics.reduce((sum, ad) => sum + ad.clicks, 0);
  const totalSpend = adsWithAnalytics.reduce(
    (sum, ad) => sum + (ad.spend || 0),
    0,
  );
  const averageCTR = totalImpressions > 0 ? totalClicks / totalImpressions : 0;

  return {
    totalImpressions,
    totalClicks,
    totalSpend,
    averageCTR,
    ads: adsWithAnalytics,
  };
}

/**
 * Calculate analytics for a specific app (model provider perspective)
 */
export async function getAppAnalytics(
  appId: string,
): Promise<AnalyticsData & { revenue: number }> {
  const impressions = await prisma.adImpression.findMany({
    where: { appId },
    select: {
      clicked: true,
      advertisement: {
        select: {
          bidType: true,
          bidAmount: true,
        },
      },
    },
  });

  const totalImpressions = impressions.length;
  const totalClicks = impressions.filter((imp) => imp.clicked).length;
  const ctr = totalImpressions > 0 ? totalClicks / totalImpressions : 0;

  // Calculate revenue (model provider gets a percentage of advertiser spend)
  // Using 30% revenue share as default
  const REVENUE_SHARE = 0.3;
  let revenue = 0;

  impressions.forEach((impression) => {
    const { bidType, bidAmount } = impression.advertisement;

    if (bidType === "CPC" && impression.clicked) {
      revenue += Number(bidAmount) * REVENUE_SHARE;
    } else if (bidType === "CPM") {
      revenue += (Number(bidAmount) / 1000) * REVENUE_SHARE;
    }
  });

  return {
    impressions: totalImpressions,
    clicks: totalClicks,
    revenue,
    ctr,
  };
}

/**
 * Calculate analytics for multiple apps belonging to a user
 */
export async function getUserAppAnalytics(userId: string): Promise<{
  totalImpressions: number;
  totalClicks: number;
  totalRevenue: number;
  apps: Array<
    { id: string; name: string } & AnalyticsData & { revenue: number }
  >;
}> {
  const userApps = await prisma.app.findMany({
    where: { userId },
    select: {
      id: true,
      name: true,
    },
  });

  const appsWithAnalytics = await Promise.all(
    userApps.map(async (app) => {
      const analytics = await getAppAnalytics(app.id);

      return {
        id: app.id,
        name: app.name,
        ...analytics,
      };
    }),
  );

  const totalImpressions = appsWithAnalytics.reduce(
    (sum, app) => sum + app.impressions,
    0,
  );
  const totalClicks = appsWithAnalytics.reduce(
    (sum, app) => sum + app.clicks,
    0,
  );
  const totalRevenue = appsWithAnalytics.reduce(
    (sum, app) => sum + app.revenue,
    0,
  );

  return {
    totalImpressions,
    totalClicks,
    totalRevenue,
    apps: appsWithAnalytics,
  };
}

/**
 * Get monthly analytics data for advertisements
 */
export async function getMonthlyAdAnalytics(
  userId: string,
): Promise<MonthlyData[]> {
  const currentYear = new Date().getFullYear();
  const monthlyData: MonthlyData[] = [];

  for (let month = 0; month < 12; month++) {
    const startDate = new Date(currentYear, month, 1);
    const endDate = new Date(currentYear, month + 1, 0, 23, 59, 59);

    const impressions = await prisma.adImpression.findMany({
      where: {
        timestamp: {
          gte: startDate,
          lte: endDate,
        },
        advertisement: {
          userId,
        },
      },
      select: {
        clicked: true,
        advertisement: {
          select: {
            bidType: true,
            bidAmount: true,
          },
        },
      },
    });

    const totalImpressions = impressions.length;
    const totalClicks = impressions.filter((imp) => imp.clicked).length;

    // Calculate spend for the month
    let spend = 0;

    impressions.forEach((impression) => {
      const { bidType, bidAmount } = impression.advertisement;

      if (bidType === "CPC" && impression.clicked) {
        spend += Number(bidAmount);
      } else if (bidType === "CPM") {
        spend += Number(bidAmount) / 1000;
      }
    });

    monthlyData.push({
      month: startDate.toLocaleString("default", { month: "short" }),
      impressions: totalImpressions,
      clicks: totalClicks,
      spend,
    });
  }

  return monthlyData;
}

/**
 * Get monthly analytics data for apps (model provider perspective)
 */
export async function getMonthlyAppAnalytics(
  userId: string,
): Promise<MonthlyData[]> {
  const currentYear = new Date().getFullYear();
  const monthlyData: MonthlyData[] = [];
  const REVENUE_SHARE = 0.3;

  for (let month = 0; month < 12; month++) {
    const startDate = new Date(currentYear, month, 1);
    const endDate = new Date(currentYear, month + 1, 0, 23, 59, 59);

    const impressions = await prisma.adImpression.findMany({
      where: {
        timestamp: {
          gte: startDate,
          lte: endDate,
        },
        app: {
          userId,
        },
      },
      select: {
        clicked: true,
        advertisement: {
          select: {
            bidType: true,
            bidAmount: true,
          },
        },
      },
    });

    const totalImpressions = impressions.length;
    const totalClicks = impressions.filter((imp) => imp.clicked).length;

    // Calculate revenue for the month
    let revenue = 0;

    impressions.forEach((impression) => {
      const { bidType, bidAmount } = impression.advertisement;

      if (bidType === "CPC" && impression.clicked) {
        revenue += Number(bidAmount) * REVENUE_SHARE;
      } else if (bidType === "CPM") {
        revenue += (Number(bidAmount) / 1000) * REVENUE_SHARE;
      }
    });

    monthlyData.push({
      month: startDate.toLocaleString("default", { month: "short" }),
      impressions: totalImpressions,
      clicks: totalClicks,
      revenue,
    });
  }

  return monthlyData;
}
