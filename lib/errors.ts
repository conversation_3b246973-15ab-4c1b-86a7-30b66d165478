/**
 * Centralized Error Management System
 *
 * This file contains all custom error classes used throughout the application.
 * Errors are organized by domain/service for better maintainability.
 */

// ============================================================================
// BASE API ERROR CLASS
// ============================================================================

/**
 * Base class for all API errors with HTTP status code support
 */
export class APIError extends Error {
  public readonly status: number;

  constructor(message: string, status: number = 500) {
    super(message);
    this.name = "APIError";
    this.status = status;
  }
}

// ============================================================================
// USER SERVICE ERRORS
// ============================================================================

export class UserNotFoundError extends APIError {
  constructor(message: string = "User not found") {
    super(message, 404);
    this.name = "UserNotFoundError";
  }
}

export class UserAlreadyExistsError extends APIError {
  constructor(message: string = "User with this email already exists") {
    super(message, 409);
    this.name = "UserAlreadyExistsError";
  }
}

export class EmailAlreadyTakenError extends APIError {
  constructor(message: string = "Email is already taken by another user") {
    super(message, 409);
    this.name = "EmailAlreadyTakenError";
  }
}

export class InvalidRolesError extends APIError {
  constructor(message: string) {
    super(message, 400);
    this.name = "InvalidRolesError";
  }
}

// ============================================================================
// EMAIL SERVICE ERRORS
// ============================================================================

export class InvalidVerificationTokenError extends APIError {
  constructor(message: string = "Invalid or expired verification token") {
    super(message, 400);
    this.name = "InvalidVerificationTokenError";
  }
}

export class EmailAlreadyVerifiedError extends APIError {
  constructor(message: string = "Email is already verified") {
    super(message, 409);
    this.name = "EmailAlreadyVerifiedError";
  }
}

export class EmailSendError extends APIError {
  constructor(message: string = "Failed to send email") {
    super(message, 500);
    this.name = "EmailSendError";
  }
}

// ============================================================================
// AUTHENTICATION SERVICE ERRORS
// ============================================================================

export class AuthenticationError extends APIError {
  constructor(message: string = "Authentication failed") {
    super(message, 401);
    this.name = "AuthenticationError";
  }
}

export class EmailNotVerifiedError extends APIError {
  constructor(message: string = "Email not verified") {
    super(message, 403);
    this.name = "EmailNotVerifiedError";
  }
}

export class InvalidCredentialsError extends APIError {
  constructor(message: string = "Invalid credentials") {
    super(message, 401);
    this.name = "InvalidCredentialsError";
  }
}

export class PasswordMismatchError extends APIError {
  constructor(message: string = "Current password is incorrect") {
    super(message, 400);
    this.name = "PasswordMismatchError";
  }
}

// ============================================================================
// ANALYTICS SERVICE ERRORS
// ============================================================================

export class InvalidRoleError extends APIError {
  constructor(
    message: string = "Invalid role. Must be 'model' or 'advertiser'",
  ) {
    super(message, 403);
    this.name = "InvalidRoleError";
  }
}

export class InsufficientPermissionsError extends APIError {
  constructor(message: string = "Insufficient permissions") {
    super(message, 403);
    this.name = "InsufficientPermissionsError";
  }
}

export class AnalyticsDataError extends APIError {
  constructor(message: string) {
    super(message, 500);
    this.name = "AnalyticsDataError";
  }
}

// ============================================================================
// ADVERTISEMENT SERVICE ERRORS
// ============================================================================

export class AdNotFoundError extends APIError {
  constructor(message: string = "Advertisement not found") {
    super(message, 404);
    this.name = "AdNotFoundError";
  }
}

export class NoAdsAvailableError extends APIError {
  constructor(message: string = "No advertisements available") {
    super(message, 404);
    this.name = "NoAdsAvailableError";
  }
}

export class AdNotActiveError extends APIError {
  constructor(message: string = "Ad or app is not active") {
    super(message, 400);
    this.name = "AdNotActiveError";
  }
}

// ============================================================================
// APPLICATION SERVICE ERRORS
// ============================================================================

export class AppNotFoundError extends APIError {
  constructor(message: string = "Application not found") {
    super(message, 404);
    this.name = "AppNotFoundError";
  }
}

export class AppNotActiveError extends APIError {
  constructor(message: string = "Application is not active") {
    super(message, 400);
    this.name = "AppNotActiveError";
  }
}

export class InvalidAppCredentialsError extends APIError {
  constructor(message: string = "Invalid app credentials") {
    super(message, 401);
    this.name = "InvalidAppCredentialsError";
  }
}

// ============================================================================
// FILE SERVICE ERRORS
// ============================================================================

export class FileTooLargeError extends APIError {
  constructor(message: string = "File is too large") {
    super(message, 413);
    this.name = "FileTooLargeError";
  }
}

export class FileInvalidTypeError extends APIError {
  constructor(message: string = "Invalid file type") {
    super(message, 415);
    this.name = "FileInvalidTypeError";
  }
}

// ============================================================================
// PAYMENT SERVICE ERRORS
// ============================================================================

export class StripeError extends APIError {
  constructor(message: string = "Stripe operation failed") {
    super(message, 400);
    this.name = "StripeError";
  }
}

export class PaymentError extends APIError {
  constructor(message: string = "Payment processing failed") {
    super(message, 400);
    this.name = "PaymentError";
  }
}

export class PaymentNotFoundError extends APIError {
  constructor(message: string = "Payment not found") {
    super(message, 404);
    this.name = "PaymentNotFoundError";
  }
}

export class InsufficientBudgetError extends APIError {
  constructor(message: string = "Insufficient budget for this operation") {
    super(message, 400);
    this.name = "InsufficientBudgetError";
  }
}

export class StripeAccountNotFoundError extends APIError {
  constructor(message: string = "Stripe Connect account not found") {
    super(message, 404);
    this.name = "StripeAccountNotFoundError";
  }
}

export class StripeAccountNotEnabledError extends APIError {
  constructor(
    message: string = "Stripe Connect account is not enabled for payments",
  ) {
    super(message, 400);
    this.name = "StripeAccountNotEnabledError";
  }
}

export class PayoutError extends APIError {
  constructor(message: string = "Payout processing failed") {
    super(message, 400);
    this.name = "PayoutError";
  }
}

export class PayoutNotFoundError extends APIError {
  constructor(message: string = "Payout not found") {
    super(message, 404);
    this.name = "PayoutNotFoundError";
  }
}

export class StripeWebhookError extends APIError {
  constructor(message: string = "Stripe webhook processing failed") {
    super(message, 400);
    this.name = "StripeWebhookError";
  }
}

export class InvalidPaymentAmountError extends APIError {
  constructor(message: string = "Invalid payment amount") {
    super(message, 400);
    this.name = "InvalidPaymentAmountError";
  }
}

export class BudgetExceededError extends APIError {
  constructor(message: string = "Budget limit exceeded") {
    super(message, 400);
    this.name = "BudgetExceededError";
  }
}

// ============================================================================
// GROUPED EXPORTS FOR CONVENIENCE
// ============================================================================

// User-related errors
export const UserErrors = {
  UserNotFoundError,
  UserAlreadyExistsError,
  EmailAlreadyTakenError,
  InvalidRolesError,
} as const;

// Email-related errors
export const EmailErrors = {
  InvalidVerificationTokenError,
  EmailAlreadyVerifiedError,
  EmailSendError,
} as const;

// Authentication-related errors
export const AuthErrors = {
  AuthenticationError,
  EmailNotVerifiedError,
  InvalidCredentialsError,
  PasswordMismatchError,
} as const;

// Analytics-related errors
export const AnalyticsErrors = {
  InvalidRoleError,
  InsufficientPermissionsError,
  AnalyticsDataError,
} as const;

// Advertisement-related errors
export const AdErrors = {
  AdNotFoundError,
  NoAdsAvailableError,
  AdNotActiveError,
} as const;

// Application-related errors
export const AppErrors = {
  AppNotFoundError,
  AppNotActiveError,
  InvalidAppCredentialsError,
} as const;

// File-related errors
export const FileErrors = {
  FileTooLargeError,
  FileInvalidTypeError,
} as const;

// Payment-related errors
export const PaymentErrors = {
  PaymentError,
  PaymentNotFoundError,
  InsufficientBudgetError,
  StripeAccountNotFoundError,
  StripeAccountNotEnabledError,
  PayoutError,
  PayoutNotFoundError,
  StripeWebhookError,
  InvalidPaymentAmountError,
  BudgetExceededError,
} as const;

// All errors for convenience
export const AllErrors = {
  ...UserErrors,
  ...EmailErrors,
  ...AuthErrors,
  ...AnalyticsErrors,
  ...AdErrors,
  ...AppErrors,
  ...FileErrors,
  ...PaymentErrors,
} as const;
