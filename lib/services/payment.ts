import Stripe from "stripe";
import { Advertisement, Role } from "@prisma/client";

import { prisma } from "@/lib/db";
import {
  StripeError,
  PaymentError,
  InsufficientBudgetError,
  StripeAccountNotFoundError,
  StripeAccountNotEnabledError,
  PayoutError,
  InvalidPaymentAmountError,
  InvalidRolesError,
} from "@/lib/errors";

// Initialize Stripe
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export interface CreateStripeAccountData {
  userId: string;
  country?: string;
  email?: string;
  businessType?: "individual" | "company";
}

export interface CreatePaymentData {
  userId: string;
  amount: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface CreatePayoutData {
  userId: string;
  amount: number;
  currency?: string;
  description?: string;
}

/**
 * Payment Service - Business logic layer for Stripe Connect payment operations
 * Handles Connect account management, payments, payouts, and budget operations
 * Uses Stripe API directly as the source of truth for payment data
 */
export class PaymentService {
  /**
   * Create a Stripe Connect Express account for a model provider
   */
  static async createStripeAccount(
    data: CreateStripeAccountData,
  ): Promise<Stripe.Account> {
    const { userId, country = "US", email, businessType = "individual" } = data;

    // Verify user exists and has MODEL_PROVIDER role
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        roles: true,
        stripeConnectAccountId: true,
      },
    });

    if (!user.roles.includes(Role.MODEL_PROVIDER)) {
      throw new InvalidRolesError(
        "User must have MODEL_PROVIDER role to create Stripe account",
      );
    }

    if (user.stripeConnectAccountId) {
      throw new PaymentError("User already has a Stripe Connect account");
    }

    try {
      // Create Stripe Express account
      const account = await stripe.accounts.create({
        type: "express",
        country: country.toLowerCase(),
        email: email || user.email,
        business_type: businessType,
        capabilities: {
          transfers: { requested: true },
        },
        metadata: {
          platform_user_id: userId,
        },
      });

      // Save account ID to user record
      await prisma.user.update({
        where: { id: userId },
        data: { stripeConnectAccountId: account.id },
      });

      return account;
    } catch (error) {
      console.error("Failed to create Stripe account:", error);
      throw new StripeError("Failed to create Stripe Connect account");
    }
  }

  /**
   * Create onboarding link for Stripe Connect account
   */
  static async createOnboardingLink(
    userId: string,
    returnUrl: string,
    refreshUrl: string,
  ): Promise<string> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeConnectAccountId: true },
    });

    if (!user.stripeConnectAccountId) {
      throw new StripeAccountNotFoundError();
    }

    try {
      const accountLink = await stripe.accountLinks.create({
        account: user.stripeConnectAccountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: "account_onboarding",
      });

      return accountLink.url;
    } catch (error) {
      console.error("Failed to create onboarding link:", error);
      throw new StripeError("Failed to create onboarding link");
    }
  }

  /**
   * Create Express dashboard link for connected account
   */
  static async createDashboardLink(userId: string): Promise<string> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeConnectAccountId: true },
    });

    if (!user.stripeConnectAccountId) {
      throw new StripeAccountNotFoundError();
    }

    try {
      // Get account status from Stripe
      const account = await stripe.accounts.retrieve(
        user.stripeConnectAccountId,
      );

      if (!account.charges_enabled) {
        throw new StripeAccountNotEnabledError();
      }

      const loginLink = await stripe.accounts.createLoginLink(
        user.stripeConnectAccountId,
      );

      return loginLink.url;
    } catch (error) {
      console.error("Failed to create dashboard link:", error);
      throw new StripeError("Failed to create dashboard link");
    }
  }

  /**
   * Create a payment for advertiser budget deposit
   */
  static async createPayment(
    data: CreatePaymentData,
  ): Promise<Stripe.PaymentIntent> {
    const { userId, amount, currency = "usd", description, metadata } = data;

    if (amount <= 0) {
      throw new InvalidPaymentAmountError();
    }

    // Verify user exists and has ADVERTISER role
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { id: true, roles: true, stripeCustomerId: true },
    });

    if (!user.roles.includes(Role.ADVERTISER)) {
      throw new InvalidRolesError(
        "User must have ADVERTISER role to make payments",
      );
    }

    try {
      // Create or retrieve Stripe customer
      let customerId = user.stripeCustomerId;

      if (!customerId) {
        const customer = await stripe.customers.create({
          metadata: { platform_user_id: userId },
        });

        customerId = customer.id;

        // Update user with customer ID
        await prisma.user.update({
          where: { id: userId },
          data: { stripeCustomerId: customerId },
        });
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        customer: customerId,
        description: description || "Budget deposit",
        metadata: {
          platform_user_id: userId,
          ...metadata,
        },
      });

      return paymentIntent;
    } catch (error) {
      console.error("Failed to create payment:", error);
      throw new PaymentError("Failed to create payment");
    }
  }

  /**
   * Process budget deduction for ad spend
   */
  static async processBudgetDeduction(
    advertisementId: string,
    amount: number,
  ): Promise<{
    advertisement: Advertisement;
    deductedAmount: number;
  }> {
    if (amount <= 0) {
      throw new InvalidPaymentAmountError();
    }

    return await prisma.$transaction(async (tx) => {
      // Get advertisement with current budget
      const ad = await tx.advertisement.findUniqueOrThrow({
        where: { id: advertisementId },
        select: {
          id: true,
          remainingBudget: true,
        },
      });

      // Check if sufficient budget remains
      if (ad.remainingBudget.toNumber() < amount) {
        throw new InsufficientBudgetError();
      }

      // Deduct from remaining budget
      const updatedAd = await tx.advertisement.update({
        where: { id: advertisementId },
        data: {
          remainingBudget: {
            decrement: amount,
          },
        },
      });

      return {
        advertisement: updatedAd,
        deductedAmount: amount,
      };
    });
  }

  /**
   * Create a payout for model provider earnings
   */
  static async createPayout(data: CreatePayoutData): Promise<Stripe.Transfer> {
    const { userId, amount, currency = "usd", description } = data;

    if (amount <= 0) {
      throw new InvalidPaymentAmountError();
    }

    // Verify user has Stripe Connect account
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeConnectAccountId: true },
    });

    if (!user.stripeConnectAccountId) {
      throw new StripeAccountNotFoundError();
    }

    try {
      // Get account status from Stripe
      const account = await stripe.accounts.retrieve(
        user.stripeConnectAccountId,
      );

      if (!account.payouts_enabled) {
        throw new StripeAccountNotEnabledError(
          "Stripe account is not enabled for payouts",
        );
      }

      // Create transfer to connected account
      const transfer = await stripe.transfers.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        destination: user.stripeConnectAccountId,
        description: description || "Model provider earnings",
        metadata: {
          platform_user_id: userId,
        },
      });

      return transfer;
    } catch (error) {
      console.error("Failed to create payout:", error);
      throw new PayoutError("Failed to create payout");
    }
  }

  /**
   * Get user's payment history from Stripe
   */
  static async getUserPayments(
    userId: string,
    limit = 100,
  ): Promise<Stripe.PaymentIntent[]> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeCustomerId: true },
    });

    if (!user.stripeCustomerId) {
      return [];
    }

    try {
      const paymentIntents = await stripe.paymentIntents.list({
        customer: user.stripeCustomerId,
        limit,
      });

      return paymentIntents.data;
    } catch (error) {
      console.error("Failed to fetch payments:", error);
      throw new PaymentError("Failed to fetch payment history");
    }
  }

  /**
   * Get user's payout history from Stripe
   */
  static async getUserPayouts(
    userId: string,
    limit = 100,
  ): Promise<Stripe.Transfer[]> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeConnectAccountId: true },
    });

    if (!user.stripeConnectAccountId) {
      return [];
    }

    try {
      const transfers = await stripe.transfers.list({
        destination: user.stripeConnectAccountId,
        limit,
      });

      return transfers.data;
    } catch (error) {
      console.error("Failed to fetch payouts:", error);
      throw new PayoutError("Failed to fetch payout history");
    }
  }

  /**
   * Get Stripe Connect account status
   */
  static async getUserStripeAccount(userId: string): Promise<Stripe.Account> {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { stripeConnectAccountId: true },
    });

    if (!user.stripeConnectAccountId) {
      throw new StripeAccountNotFoundError();
    }

    try {
      // Fetch latest account info from Stripe
      const account = await stripe.accounts.retrieve(
        user.stripeConnectAccountId,
      );

      return account;
    } catch (error) {
      console.error("Failed to get Stripe account status:", error);
      throw new PaymentError("Failed to retrieve account status");
    }
  }

  /**
   * Update advertisement budget (add funds)
   */
  static async updateAdvertisementBudget(
    advertisementId: string,
    additionalAmount: number,
  ) {
    if (additionalAmount <= 0) {
      throw new InvalidPaymentAmountError();
    }

    return await prisma.$transaction(async (tx) => {
      const ad = await tx.advertisement.findUnique({
        where: { id: advertisementId },
        select: { id: true, userId: true, budget: true, remainingBudget: true },
      });

      if (!ad) {
        throw new PaymentError("Advertisement not found");
      }

      // Update both total budget and remaining budget
      const updatedAd = await tx.advertisement.update({
        where: { id: advertisementId },
        data: {
          budget: {
            increment: additionalAmount,
          },
          remainingBudget: {
            increment: additionalAmount,
          },
        },
      });

      return {
        advertisement: updatedAd,
        addedAmount: additionalAmount,
      };
    });
  }
}
