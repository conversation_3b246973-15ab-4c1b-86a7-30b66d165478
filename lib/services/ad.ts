import { AdStatus, BidType } from "@prisma/client";
import { Decimal } from "@prisma/client/runtime/library";

import { PaymentService } from "./payment";

import { prisma } from "@/lib/db";
import { getAdAnalytics } from "@/lib/analytics";
import {
  AuthenticationError,
  InvalidAppCredentialsError,
  NoAdsAvailableError,
  AdNotActiveError,
  InsufficientBudgetError,
} from "@/lib/errors";

export interface CreateAdData {
  userId: string;
  name: string;
  description: string;
  productUrl: string;
  imageUrl?: string;
  targetTopics: string[];
  budget: number;
  bidType: BidType;
  bidAmount: number;
}

export interface UpdateAdData {
  name?: string;
  description?: string;
  productUrl?: string;
  imageUrl?: string;
  targetTopics?: string[];
  budget?: number;
  bidType?: BidType;
  bidAmount?: number;
  status?: AdStatus;
}

export interface AdWithAnalytics {
  id: string;
  name: string;
  description: string;
  imageUrl: string | null;
  productUrl: string;
  targetTopics: string[];
  budget: number;
  bidType: BidType;
  bidAmount: number;
  status: AdStatus;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
}

export interface ServeAdRequest {
  appId: string;
  appSecret: string;
  topics?: string[];
  userContext?: {
    userAgent?: string;
    language?: string;
  };
}

export interface ServedAd {
  id: string;
  name: string;
  description: string;
  imageUrl: string | null;
  productUrl: string;
  targetTopics: string[];
}

/**
 * Ad Service - Business logic layer for advertisement operations
 * Handles ad creation, updates, serving, and analytics
 */
export class AdService {
  /**
   * Create a new advertisement
   */
  static async createAd(data: CreateAdData): Promise<AdWithAnalytics> {
    const {
      userId,
      name,
      description,
      productUrl,
      imageUrl,
      targetTopics,
      budget,
      bidType,
      bidAmount,
    } = data;

    // Validate user exists and has ADVERTISER role
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { id: true, roles: true },
    });

    if (!user.roles.includes("ADVERTISER")) {
      throw new AuthenticationError(
        "User must have ADVERTISER role to create ads",
      );
    }

    // Create advertisement
    const ad = await prisma.advertisement.create({
      data: {
        userId,
        name,
        description,
        productUrl,
        imageUrl: imageUrl || null,
        targetTopics: Array.isArray(targetTopics) ? targetTopics : [],
        budget: new Decimal(budget),
        remainingBudget: new Decimal(budget), // Initialize remaining budget to full budget
        bidType,
        bidAmount: new Decimal(bidAmount),
        status: AdStatus.ACTIVE,
      },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    // Get analytics data
    const analytics = await getAdAnalytics(ad.id);

    return {
      ...ad,
      budget: Number(ad.budget),
      bidAmount: Number(ad.bidAmount),
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      spend: Number(analytics.spend) || 0,
      ctr: Number(analytics.ctr) || 0,
    };
  }

  /**
   * Get advertisement by ID
   */
  static async getAdById(adId: string): Promise<AdWithAnalytics> {
    const ad = await prisma.advertisement.findUniqueOrThrow({
      where: { id: adId },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAdAnalytics(ad.id);

    const adWithAnalytics: AdWithAnalytics = {
      ...ad,
      budget: Number(ad.budget),
      bidAmount: Number(ad.bidAmount),
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      spend: Number(analytics.spend) || 0,
      ctr: Number(analytics.ctr) || 0,
    };

    return adWithAnalytics;
  }

  /**
   * Get all advertisements for a user
   */
  static async getUserAds(userId: string): Promise<AdWithAnalytics[]> {
    const ads = await prisma.advertisement.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get analytics for each ad
    const adsWithAnalytics = await Promise.all(
      ads.map(async (ad) => {
        const analytics = await getAdAnalytics(ad.id);

        return {
          ...ad,
          budget: Number(ad.budget),
          bidAmount: Number(ad.bidAmount),
          impressions: analytics.impressions,
          clicks: analytics.clicks,
          spend: Number(analytics.spend) || 0,
          ctr: Number(analytics.ctr) || 0,
        };
      }),
    );

    return adsWithAnalytics;
  }

  /**
   * Update advertisement
   */
  static async updateAd(
    adId: string,
    data: UpdateAdData,
    userId?: string,
  ): Promise<AdWithAnalytics> {
    const {
      name,
      description,
      productUrl,
      imageUrl,
      targetTopics,
      budget,
      bidType,
      bidAmount,
      status,
    } = data;

    // Build where clause
    const whereClause: any = { id: adId };

    if (userId) {
      whereClause.userId = userId; // Ensure user can only update their own ads
    }

    // Prepare update data
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (productUrl !== undefined) updateData.productUrl = productUrl;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;
    if (targetTopics !== undefined) updateData.targetTopics = targetTopics;
    if (budget !== undefined) updateData.budget = new Decimal(budget);
    if (bidType !== undefined) updateData.bidType = bidType;
    if (bidAmount !== undefined) updateData.bidAmount = new Decimal(bidAmount);
    if (status !== undefined) updateData.status = status;

    const updatedAd = await prisma.advertisement.update({
      where: whereClause,
      data: updateData,
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        budget: true,
        bidType: true,
        bidAmount: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAdAnalytics(updatedAd.id);

    const adWithAnalytics: AdWithAnalytics = {
      ...updatedAd,
      budget: Number(updatedAd.budget),
      bidAmount: Number(updatedAd.bidAmount),
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      spend: Number(analytics.spend) || 0,
      ctr: Number(analytics.ctr) || 0,
    };

    return adWithAnalytics;
  }

  /**
   * Delete advertisement
   */
  static async deleteAd(adId: string, userId: string): Promise<void> {
    await prisma.advertisement.delete({
      where: { id: adId, userId },
    });
  }

  /**
   * Serve an advertisement based on request criteria
   */
  static async serveAd(request: ServeAdRequest): Promise<{
    ad: ServedAd;
    appId: string;
    trackingUrl: string;
    instructions: {
      impression: string;
      click: string;
    };
  }> {
    const { appId, appSecret, topics } = request;

    // Verify app credentials
    const app = await prisma.app.findUniqueOrThrow({
      where: { appId },
      select: {
        id: true,
        appSecret: true,
        status: true,
        name: true,
      },
    });

    if (app.appSecret !== appSecret || app.status !== "ACTIVE") {
      throw new InvalidAppCredentialsError();
    }

    // Find matching advertisements with sufficient budget
    let whereClause: any = {
      status: AdStatus.ACTIVE,
      remainingBudget: {
        gt: 0, // Only include ads with remaining budget
      },
    };

    // Filter by topics if provided
    if (topics && topics.length > 0) {
      whereClause.targetTopics = {
        hasSome: topics,
      };
    }

    const ads = await prisma.advertisement.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        description: true,
        imageUrl: true,
        productUrl: true,
        targetTopics: true,
        bidAmount: true,
        bidType: true,
        remainingBudget: true,
      },
    });

    if (ads.length === 0) {
      throw new NoAdsAvailableError();
    }

    // Weight ads by bid amount and add randomness
    const weightedAds = ads.map((ad) => ({
      ...ad,
      bidAmount: Number(ad.bidAmount),
      weight: Number(ad.bidAmount) * (0.5 + Math.random() * 0.5), // 50% bid, 50% random
    }));

    // Sort by weight (higher bid + randomness = higher chance)
    weightedAds.sort((a, b) => b.weight - a.weight);

    const selectedAd = weightedAds[0];

    const servedAd: ServedAd = {
      id: selectedAd.id,
      name: selectedAd.name,
      description: selectedAd.description,
      imageUrl: selectedAd.imageUrl,
      productUrl: selectedAd.productUrl,
      targetTopics: selectedAd.targetTopics,
    };

    return {
      ad: servedAd,
      appId: app.id,
      trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/impressions`,
      instructions: {
        impression: "POST to trackingUrl with { adId, appId, clicked: false }",
        click: "POST to trackingUrl with { adId, appId, clicked: true }",
      },
    };
  }

  /**
   * Record ad impression or click and deduct from budget
   */
  static async recordImpression(
    adId: string,
    appId: string,
    clicked: boolean,
    userAgent?: string,
    ipAddress?: string,
  ): Promise<void> {
    // Use transaction to ensure atomicity
    await prisma.$transaction(async (tx) => {
      // Verify ad and app exist and get bid amount
      const [ad, app] = await Promise.all([
        tx.advertisement.findUniqueOrThrow({
          where: { id: adId },
          select: {
            id: true,
            status: true,
            bidAmount: true,
            remainingBudget: true,
            bidType: true,
          },
        }),
        tx.app.findUniqueOrThrow({
          where: { id: appId },
          select: { id: true, status: true },
        }),
      ]);

      if (ad.status !== AdStatus.ACTIVE || app.status !== "ACTIVE") {
        throw new AdNotActiveError();
      }

      // Calculate cost based on bid type and action
      let cost = 0;

      if (ad.bidType === BidType.CPM) {
        // Cost per mille (1000 impressions)
        cost = Number(ad.bidAmount) / 1000;
      } else if (ad.bidType === BidType.CPC && clicked) {
        // Cost per click - only charge for clicks
        cost = Number(ad.bidAmount);
      }

      // Check if sufficient budget remains
      if (cost > 0 && Number(ad.remainingBudget) < cost) {
        throw new InsufficientBudgetError();
      }

      // Record impression
      await tx.adImpression.create({
        data: {
          adId,
          appId,
          clicked,
          userAgent: userAgent || null,
          ipAddress: ipAddress || null,
        },
      });

      // Deduct cost from remaining budget if there's a cost
      if (cost > 0) {
        await PaymentService.processBudgetDeduction({
          advertisementId: adId,
          amount: cost,
          description: clicked
            ? `Click charge (${ad.bidType})`
            : `Impression charge (${ad.bidType})`,
        });
      }
    });
  }

  /**
   * Get advertisement analytics
   */
  static async getAdAnalytics(
    adId: string,
    userId?: string,
  ): Promise<{
    adId: string;
    adName: string;
    analytics: any;
  }> {
    // Verify ad exists and user has access
    const whereClause: any = { id: adId };

    if (userId) {
      whereClause.userId = userId;
    }

    const ad = await prisma.advertisement.findUniqueOrThrow({
      where: whereClause,
      select: { id: true, name: true },
    });

    // Get analytics data
    const analytics = await getAdAnalytics(ad.id);

    return {
      adId: ad.id,
      adName: ad.name,
      analytics,
    };
  }

  /**
   * Get advertisement statistics
   */
  static async getAdStats(): Promise<{
    totalAds: number;
    activeAds: number;
    pausedAds: number;
    completedAds: number;
  }> {
    const [totalAds, activeAds, pausedAds, completedAds] = await Promise.all([
      prisma.advertisement.count(),
      prisma.advertisement.count({
        where: { status: AdStatus.ACTIVE },
      }),
      prisma.advertisement.count({
        where: { status: AdStatus.PAUSED },
      }),
      prisma.advertisement.count({
        where: { status: AdStatus.COMPLETED },
      }),
    ]);

    return {
      totalAds,
      activeAds,
      pausedAds,
      completedAds,
    };
  }
}
