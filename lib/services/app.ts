import { AppStatus } from "@prisma/client";
import { nanoid } from "nanoid";

import { prisma } from "@/lib/db";
import { getAppAnalytics as getAnalyticsData } from "@/lib/analytics";
import {
  AuthenticationError,
  InvalidAppCredentialsError,
  AppNotActiveError,
} from "@/lib/errors";

export interface UpdateAppData {
  name?: string;
  description?: string;
  status?: AppStatus;
}

export interface AppWithAnalytics {
  id: string;
  name: string;
  appId: string;
  appSecret?: string;
  description: string | null;
  status: AppStatus;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  impressions: number;
  clicks: number;
  revenue: number;
  ctr: number;
}

export interface AppCredentials {
  appId: string;
  appSecret: string;
}

/**
 * App Service - Business logic layer for application management operations
 * Handles app creation, updates, credential management, and analytics
 */
export class AppService {
  /**
   * Create a new app for a user
   */
  static async createApp(
    userId: string,
    name: string,
    description?: string,
  ): Promise<AppWithAnalytics> {
    // Validate user exists and has MODEL_PROVIDER role
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { id: true, roles: true },
    });

    if (!user.roles.includes("MODEL_PROVIDER")) {
      throw new AuthenticationError(
        "User must have MODEL_PROVIDER role to create apps",
      );
    }

    // Generate unique app ID and secret
    const appId = `app_${nanoid(16)}`;
    const appSecret = `secret_${nanoid(32)}`;

    // Create app
    const app = await prisma.app.create({
      data: {
        userId,
        name,
        appId,
        appSecret,
        description: description || null,
        status: AppStatus.ACTIVE,
      },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      ...app,
      impressions: 0,
      clicks: 0,
      revenue: 0,
      ctr: 0,
    };
  }

  /**
   * Get app by ID
   */
  static async getAppById(
    appId: string,
    includeSecret = false,
  ): Promise<AppWithAnalytics> {
    const app = await prisma.app.findUniqueOrThrow({
      where: { id: appId },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: includeSecret,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAnalyticsData(app.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...app,
      appSecret: includeSecret ? app.appSecret : undefined,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return appWithAnalytics;
  }

  /**
   * Get app by appId (public identifier)
   */
  static async getAppByAppId(
    appId: string,
    includeSecret = false,
  ): Promise<AppWithAnalytics> {
    const app = await prisma.app.findUniqueOrThrow({
      where: { appId },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: includeSecret,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAnalyticsData(app.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...app,
      appSecret: includeSecret ? app.appSecret : undefined,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return appWithAnalytics;
  }

  /**
   * Get all apps for a user
   */
  static async getUserApps(userId: string): Promise<AppWithAnalytics[]> {
    const apps = await prisma.app.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get analytics for each app
    const appsWithAnalytics = await Promise.all(
      apps.map(async (app) => {
        const analytics = await getAnalyticsData(app.id);

        return {
          ...app,
          impressions: analytics.impressions,
          clicks: analytics.clicks,
          revenue: analytics.revenue,
          ctr: analytics.ctr,
        };
      }),
    );

    return appsWithAnalytics;
  }

  /**
   * Update app information
   */
  static async updateApp(
    appId: string,
    data: UpdateAppData,
  ): Promise<AppWithAnalytics> {
    const updatedApp = await prisma.app.update({
      where: { id: appId },
      data: data,
      select: {
        id: true,
        name: true,
        appId: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Get analytics data
    const analytics = await getAnalyticsData(updatedApp.id);

    const appWithAnalytics: AppWithAnalytics = {
      ...updatedApp,
      impressions: analytics.impressions,
      clicks: analytics.clicks,
      revenue: analytics.revenue,
      ctr: analytics.ctr,
    };

    return appWithAnalytics;
  }

  /**
   * Delete app
   */
  static async deleteApp(appId: string, userId: string): Promise<void> {
    await prisma.app.delete({
      where: { id: appId, userId },
    });
  }

  /**
   * Verify app credentials
   */
  static async verifyAppCredentials(
    appId: string,
    appSecret: string,
  ): Promise<{
    id: string;
    name: string;
    status: AppStatus;
    userId: string;
  }> {
    const app = await prisma.app.findUniqueOrThrow({
      where: { appId },
      select: {
        id: true,
        name: true,
        appSecret: true,
        status: true,
        userId: true,
      },
    });

    if (app.appSecret !== appSecret) {
      throw new InvalidAppCredentialsError();
    }

    if (app.status !== AppStatus.ACTIVE) {
      throw new AppNotActiveError();
    }

    return {
      id: app.id,
      name: app.name,
      status: app.status,
      userId: app.userId,
    };
  }

  /**
   * Regenerate app secret
   */
  static async regenerateAppSecret(appId: string): Promise<string> {
    // Generate new secret
    const newAppSecret = `secret_${nanoid(32)}`;

    await prisma.app.update({
      where: { id: appId },
      data: { appSecret: newAppSecret },
      select: {
        id: true,
        name: true,
        appId: true,
        appSecret: true,
        description: true,
        status: true,
        userId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return newAppSecret;
  }
}
