import { nanoid } from "nanoid";

import { supabase } from "../supabase";
import { FileTooLargeError, FileInvalidTypeError } from "../errors";

export interface UploadFileData {
  file: File;
  userId?: string; // Optional for authenticated uploads
}

export interface UploadedFile {
  url: string;
  filename: string;
  size: number;
  type: string;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
];
const UPLOAD_BUCKET = "uploads";

/**
 * File Service - Business logic layer for file upload operations
 * Handles file validation, storage, and metadata management using Supabase
 */
export class FileService {
  /**
   * Upload a file to Supabase Storage
   */
  static async uploadFile(file: File): Promise<UploadedFile> {
    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      throw new FileInvalidTypeError();
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      throw new FileTooLargeError();
    }

    // Generate unique filename
    const fileExtension = file.name.split(".").pop();
    const uniqueFilename = `${nanoid(16)}.${fileExtension}`;

    // Upload file to Supabase
    const { error } = await supabase.storage
      .from(UPLOAD_BUCKET)
      .upload(uniqueFilename, file);

    if (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from(UPLOAD_BUCKET)
      .getPublicUrl(uniqueFilename);

    if (!publicUrlData) {
      throw new Error("Could not get public URL for the uploaded file.");
    }

    return {
      url: publicUrlData.publicUrl,
      filename: uniqueFilename,
      size: file.size,
      type: file.type,
    };
  }

  /**
   * Get allowed file types
   */
  static getAllowedTypes(): string[] {
    return [...ALLOWED_TYPES];
  }

  /**
   * Get maximum file size
   */
  static getMaxFileSize(): number {
    return MAX_FILE_SIZE;
  }
}
