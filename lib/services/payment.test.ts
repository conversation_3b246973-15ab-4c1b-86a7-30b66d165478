import { describe, it, expect, vi, beforeEach } from "vitest";
import { Role } from "@prisma/client";

import { PaymentService } from "./payment";

import { prisma } from "@/lib/db";
import {
  UserNotFoundError,
  InvalidRolesError,
  PaymentError,
  StripeAccountNotFoundError,
  InvalidPaymentAmountError,
  InsufficientBudgetError,
} from "@/lib/errors";

// Mock Stripe
vi.mock("stripe", () => {
  const mockStripe = {
    accounts: {
      create: vi.fn(),
      retrieve: vi.fn(),
      createLoginLink: vi.fn(),
    },
    accountLinks: {
      create: vi.fn(),
    },
    customers: {
      create: vi.fn(),
    },
    paymentIntents: {
      create: vi.fn(),
      list: vi.fn(),
    },
    transfers: {
      create: vi.fn(),
      list: vi.fn(),
    },
  };

  return {
    default: vi.fn().mockImplementation(() => mockStripe),
  };
});

// Get access to the mocked Stripe instance
const mockStripe = {
  accounts: {
    create: vi.fn(),
    retrieve: vi.fn(),
    createLoginLink: vi.fn(),
  },
  accountLinks: {
    create: vi.fn(),
  },
  customers: {
    create: vi.fn(),
  },
  paymentIntents: {
    create: vi.fn(),
    list: vi.fn(),
  },
  transfers: {
    create: vi.fn(),
    list: vi.fn(),
  },
};

// Mock Prisma
vi.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    advertisement: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

describe("PaymentService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("createStripeAccount", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      roles: [Role.MODEL_PROVIDER],
      stripeConnectAccountId: null,
    };

    const mockStripeAccount = {
      id: "acct_123",
      charges_enabled: false,
      payouts_enabled: false,
      details_submitted: false,
      country: "US",
    };

    it("should create a Stripe Connect account successfully", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(mockStripe.accounts.create).mockResolvedValue(
        mockStripeAccount,
      );
      vi.mocked(prisma.user.update).mockResolvedValue({
        ...mockUser,
        stripeConnectAccountId: "acct_123",
      });

      const result = await PaymentService.createStripeAccount({
        userId: "user-123",
        email: "<EMAIL>",
      });

      expect(mockStripe.accounts.create).toHaveBeenCalledWith({
        type: "express",
        country: "us",
        email: "<EMAIL>",
        business_type: "individual",
        capabilities: {
          transfers: { requested: true },
        },
        metadata: {
          platform_user_id: "user-123",
        },
      });

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-123" },
        data: { stripeConnectAccountId: "acct_123" },
      });

      expect(result).toEqual({
        id: "acct_123",
        userId: "user-123",
        status: "pending",
        chargesEnabled: false,
        payoutsEnabled: false,
        country: "US",
      });
    });

    it("should throw UserNotFoundError if user doesn't exist", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(null);

      await expect(
        PaymentService.createStripeAccount({
          userId: "nonexistent",
        }),
      ).rejects.toThrow(UserNotFoundError);
    });

    it("should throw InvalidRolesError if user doesn't have MODEL_PROVIDER role", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        ...mockUser,
        roles: [Role.ADVERTISER],
      });

      await expect(
        PaymentService.createStripeAccount({
          userId: "user-123",
        }),
      ).rejects.toThrow(InvalidRolesError);
    });

    it("should throw PaymentError if user already has a Stripe account", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        ...mockUser,
        stripeConnectAccountId: "existing-account",
      });

      await expect(
        PaymentService.createStripeAccount({
          userId: "user-123",
        }),
      ).rejects.toThrow(PaymentError);
    });
  });

  describe("createOnboardingLink", () => {
    it("should create onboarding link successfully", async () => {
      const mockUser = {
        stripeConnectAccountId: "acct_123",
      };

      const mockAccountLink = {
        url: "https://connect.stripe.com/setup/...",
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(mockStripe.accountLinks.create).mockResolvedValue(
        mockAccountLink,
      );

      const result = await PaymentService.createOnboardingLink(
        "user-123",
        "https://example.com/return",
        "https://example.com/refresh",
      );

      expect(mockStripe.accountLinks.create).toHaveBeenCalledWith({
        account: "acct_123",
        refresh_url: "https://example.com/refresh",
        return_url: "https://example.com/return",
        type: "account_onboarding",
      });

      expect(result).toBe("https://connect.stripe.com/setup/...");
    });

    it("should throw StripeAccountNotFoundError if user has no Stripe account", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        stripeConnectAccountId: null,
      });

      await expect(
        PaymentService.createOnboardingLink(
          "user-123",
          "https://example.com/return",
          "https://example.com/refresh",
        ),
      ).rejects.toThrow(StripeAccountNotFoundError);
    });
  });

  describe("createPayment", () => {
    const mockUser = {
      id: "user-123",
      roles: [Role.ADVERTISER],
      stripeCustomerId: null,
    };

    const mockCustomer = {
      id: "cus_123",
    };

    const mockPaymentIntent = {
      id: "pi_123",
      client_secret: "pi_123_secret",
      status: "requires_payment_method",
    };

    it("should create payment successfully", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(mockStripe.customers.create).mockResolvedValue(mockCustomer);
      vi.mocked(mockStripe.paymentIntents.create).mockResolvedValue(
        mockPaymentIntent,
      );
      vi.mocked(prisma.user.update).mockResolvedValue({
        ...mockUser,
        stripeCustomerId: "cus_123",
      });

      const result = await PaymentService.createPayment({
        userId: "user-123",
        amount: 100,
        description: "Test payment",
      });

      expect(mockStripe.customers.create).toHaveBeenCalledWith({
        metadata: { platform_user_id: "user-123" },
      });

      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
        amount: 10000, // $100 in cents
        currency: "usd",
        customer: "cus_123",
        description: "Test payment",
        metadata: {
          platform_user_id: "user-123",
        },
      });

      expect(result).toEqual({
        paymentIntentId: "pi_123",
        clientSecret: "pi_123_secret",
        amount: 100,
        currency: "usd",
        status: "requires_payment_method",
      });
    });

    it("should throw InvalidPaymentAmountError for invalid amount", async () => {
      await expect(
        PaymentService.createPayment({
          userId: "user-123",
          amount: 0,
        }),
      ).rejects.toThrow(InvalidPaymentAmountError);
    });

    it("should throw InvalidRolesError if user doesn't have ADVERTISER role", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        ...mockUser,
        roles: [Role.MODEL_PROVIDER],
      });

      await expect(
        PaymentService.createPayment({
          userId: "user-123",
          amount: 100,
        }),
      ).rejects.toThrow(InvalidRolesError);
    });
  });

  describe("processBudgetDeduction", () => {
    const mockAd = {
      id: "ad-123",
      userId: "user-123",
      remainingBudget: { toNumber: () => 50 },
      bidAmount: { toNumber: () => 1 },
    };

    it("should deduct budget successfully", async () => {
      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        return await callback({
          advertisement: {
            findUnique: vi.fn().mockResolvedValue(mockAd),
            update: vi.fn().mockResolvedValue({
              ...mockAd,
              remainingBudget: { toNumber: () => 45 },
            }),
          },
        });
      });

      vi.mocked(prisma.$transaction).mockImplementation(mockTransaction);

      const result = await PaymentService.processBudgetDeduction({
        advertisementId: "ad-123",
        amount: 5,
        description: "Test deduction",
      });

      expect(result.deductedAmount).toBe(5);
      expect(result.description).toBe("Test deduction");
    });

    it("should throw InsufficientBudgetError when budget is insufficient", async () => {
      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        return await callback({
          advertisement: {
            findUnique: vi.fn().mockResolvedValue(mockAd),
          },
        });
      });

      vi.mocked(prisma.$transaction).mockImplementation(mockTransaction);

      await expect(
        PaymentService.processBudgetDeduction({
          advertisementId: "ad-123",
          amount: 100, // More than remaining budget
          description: "Test deduction",
        }),
      ).rejects.toThrow(InsufficientBudgetError);
    });
  });

  describe("getUserPayments", () => {
    it("should return empty array if user has no Stripe customer ID", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        stripeCustomerId: null,
      });

      const result = await PaymentService.getUserPayments("user-123");

      expect(result).toEqual([]);
    });

    it("should fetch payments from Stripe successfully", async () => {
      const mockUser = { stripeCustomerId: "cus_123" };
      const mockPaymentIntents = {
        data: [
          {
            id: "pi_123",
            amount: 10000,
            currency: "usd",
            status: "succeeded",
            description: "Test payment",
            created: **********, // 2022-01-01
          },
        ],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(mockStripe.paymentIntents.list).mockResolvedValue(
        mockPaymentIntents,
      );

      const result = await PaymentService.getUserPayments("user-123");

      expect(mockStripe.paymentIntents.list).toHaveBeenCalledWith({
        customer: "cus_123",
        limit: 100,
      });

      expect(result).toEqual([
        {
          id: "pi_123",
          amount: 100,
          currency: "usd",
          status: "succeeded",
          description: "Test payment",
          createdAt: "2022-01-01T00:00:00.000Z",
        },
      ]);
    });
  });

  describe("getUserPayouts", () => {
    it("should return empty array if user has no Stripe Connect account", async () => {
      vi.mocked(prisma.user.findUnique).mockResolvedValue({
        stripeConnectAccountId: null,
      });

      const result = await PaymentService.getUserPayouts("user-123");

      expect(result).toEqual([]);
    });

    it("should fetch payouts from Stripe successfully", async () => {
      const mockUser = { stripeConnectAccountId: "acct_123" };
      const mockTransfers = {
        data: [
          {
            id: "tr_123",
            amount: 5000,
            currency: "usd",
            description: "Model provider earnings",
            created: **********, // 2022-01-01
          },
        ],
      };

      vi.mocked(prisma.user.findUnique).mockResolvedValue(mockUser);
      vi.mocked(mockStripe.transfers.list).mockResolvedValue(mockTransfers);

      const result = await PaymentService.getUserPayouts("user-123");

      expect(mockStripe.transfers.list).toHaveBeenCalledWith({
        destination: "acct_123",
        limit: 100,
      });

      expect(result).toEqual([
        {
          id: "tr_123",
          amount: 50,
          currency: "usd",
          status: "paid",
          description: "Model provider earnings",
          createdAt: "2022-01-01T00:00:00.000Z",
        },
      ]);
    });
  });
});
