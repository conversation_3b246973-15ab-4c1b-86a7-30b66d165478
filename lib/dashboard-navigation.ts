import { Role } from "@prisma/client";

export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  description?: string;
  requiredRoles?: Role[];
}

export interface NavigationSection {
  title?: string;
  items: NavigationItem[];
}

/**
 * Dashboard navigation configuration
 * Defines the sidebar navigation structure with role-based access control
 */
export const dashboardNavigation: NavigationSection[] = [
  // Top section - Home
  {
    items: [
      {
        label: "Home",
        href: "/dashboard",
        description: "Dashboard overview",
      },
    ],
  },
  // Middle section - Role-based navigation (now handled by tabs in main dashboard)
  {
    items: [],
  },
  // Bottom section - Settings and Payments
  {
    items: [
      {
        label: "Payments",
        href: "/dashboard/payments",
        description: "Payment management",
      },
      {
        label: "Settings",
        href: "/dashboard/settings",
        description: "Account settings",
      },
    ],
  },
];

/**
 * Get navigation items filtered by user roles
 */
export function getFilteredNavigation(userRoles: Role[]): NavigationSection[] {
  return dashboardNavigation
    .map((section) => ({
      ...section,
      items: section.items.filter((item) => {
        // If no required roles, show to everyone
        if (!item.requiredRoles || item.requiredRoles.length === 0) {
          return true;
        }
        // Check if user has any of the required roles
        return item.requiredRoles.some((role) => userRoles.includes(role));
      }),
    }))
    .filter((section) => section.items.length > 0); // Remove empty sections
}

/**
 * Check if user has access to a specific route
 */
export function hasRouteAccess(route: string, userRoles: Role[]): boolean {
  const allItems = dashboardNavigation.flatMap((section) => section.items);
  const item = allItems.find((item) => item.href === route);
  
  if (!item) {
    return false;
  }
  
  if (!item.requiredRoles || item.requiredRoles.length === 0) {
    return true;
  }
  
  return item.requiredRoles.some((role) => userRoles.includes(role));
}

/**
 * Get the current active navigation item based on pathname
 */
export function getActiveNavigationItem(pathname: string): NavigationItem | null {
  const allItems = dashboardNavigation.flatMap((section) => section.items);
  
  // Find exact match first
  let activeItem = allItems.find((item) => item.href === pathname);
  
  // If no exact match, find the best partial match
  if (!activeItem) {
    const matches = allItems.filter((item) => 
      pathname.startsWith(item.href) && item.href !== "/dashboard"
    );
    
    // Sort by length (longest match first) and take the first one
    matches.sort((a, b) => b.href.length - a.href.length);
    activeItem = matches[0];
  }
  
  return activeItem || null;
}
