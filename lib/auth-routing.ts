import { Role } from "@prisma/client";
import { Session } from "next-auth";

/**
 * Determines the appropriate redirect path based on user's authentication status and roles
 */
export function getAuthRedirectPath(session: Session | null): string {
  // If no session, redirect to login
  if (!session || !session.user) {
    return "/login";
  }

  // If user has no roles assigned, redirect to setup
  if (!session.user.roles || session.user.roles.length === 0) {
    return "/setup";
  }

  // For users with roles, redirect to the unified dashboard
  // The dashboard will handle role-specific content and navigation
  return "/dashboard";
}

/**
 * Checks if user has access to a specific dashboard
 */
export function hasRoleAccess(
  session: Session | null,
  requiredRole: string,
): boolean {
  if (!session || !session.user || !session.user.roles) {
    return false;
  }

  return session.user.roles.includes(requiredRole as Role);
}

/**
 * Gets the appropriate dashboard path for a user after role assignment
 */
export function getDashboardPathAfterRoleAssignment(
  selectedRoles: string[],
): string {
  if (selectedRoles.length === 0) {
    return "/setup";
  }

  // Always redirect to the unified dashboard after role assignment
  return "/dashboard";
}
