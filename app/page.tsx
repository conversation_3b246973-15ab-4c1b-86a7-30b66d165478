// pages/index.tsx
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import NextLink from "next/link";
import clsx from "clsx";

import { title, subtitle } from "@/components/primitives";

export default function Home() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setStatus("loading");
    try {
      const res = await fetch("/api/waitlist", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email }),
      });
      const data = await res.json();

      if (!res.ok) throw new Error(data.error || "Unknown error");
      setStatus("success");
      setName("");
      setEmail("");
    } catch (err) {
      console.error(err);
      setStatus("error");
    }
  }

  return (
    <div className="flex flex-col gap-24 py-16">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center text-center px-4 md:px-0">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20 pointer-events-none blur-xl" />
        <div className="relative z-10 max-w-4xl">
          <h1 className={clsx(title({ size: "lg" }), "mt-6 leading-snug")}>
            Bring Free AI to Everyone with&nbsp;
            <span className={title({ color: "violet", size: "lg" })}>
              Contextual Advertising
            </span>
          </h1>
          <p className={subtitle({ class: "mt-6 text-lg text-default-600" })}>
            The premier platform for AI model providers to monetize their
            applications and advertisers to reach engaged AI users with
            precision targeting.
          </p>
          <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={NextLink}
              className="font-semibold transition-transform hover:-translate-y-1"
              color="primary"
              href="/register"
              size="lg"
            >
              Get Started Free
            </Button>
            <Button
              as={NextLink}
              className="font-semibold transition-opacity hover:opacity-80"
              href="/login"
              size="lg"
              variant="bordered"
            >
              Sign In
            </Button>
          </div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="max-w-screen-md mx-auto px-4 text-center">
        <div className="mb-12">
          <h2 className={title({ size: "md" })}>
            Welcome to the <br />
            <span className="text-warning">the Age of Intelligence</span>
          </h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            A powerful AI search engine that helps you find the best AI models
            and applications that you can use for free, powered by contextual
            advertising.
          </p>
        </div>
        <div className="bg-default-100 p-6 rounded-lg shadow-md">
          <p className="text-default-700">
            Note: Both Mindify Search and AiD platform are currently in
            development. Stay tuned for updates!
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-screen-lg mx-auto px-4" id="features">
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>
            Powerful Features for Both Sides
          </h2>
          <p className={subtitle({ class: "mt-4 text-default-600" })}>
            Everything you need to succeed in AI advertising
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2">
          {[
            {
              key: "providers",
              label: "For Providers",
              color: "primary",
              title: "Monetize Your AI Apps",
              items: [
                "Easy integration with secure API credentials",
                "Real-time revenue analytics and reporting",
                "Competitive CPM and CPC rates",
                "Automated ad serving and optimization",
              ],
            },
            {
              key: "advertisers",
              label: "For Advertisers",
              color: "secondary",
              title: "Reach AI Users",
              items: [
                "Target specific AI application categories",
                "Detailed performance metrics and ROI tracking",
                "Flexible budget management and bidding",
                "High-quality, engaged AI user audience",
              ],
            },
          ].map((section) => (
            <Card
              key={section.key}
              className="p-6 border-default-200 hover:shadow-lg hover:border-transparent transition-all"
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <Chip color="default" variant="flat">
                    {section.label}
                  </Chip>
                  <h3 className="text-xl font-bold text-foreground">
                    {section.title}
                  </h3>
                </div>
              </CardHeader>
              <CardBody className="pt-2">
                <ul className="space-y-3 text-default-600">
                  {section.items.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className={clsx("mt-0.5 text-", section.color)}>
                        ✓
                      </span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>
          ))}
        </div>
      </section>

      {/* Waitlist Section */}
      <section className="max-w-md mx-auto px-4 text-center" id="waitlist">
        <h2 className="text-2xl font-bold mb-2">Join the Waitlist</h2>
        <p className="mb-6 text-default-600">
          Be the first to know when we launch—enter your info below:
        </p>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <input
            required
            className="w-full p-3 border rounded"
            placeholder="Your name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          <input
            required
            className="w-full p-3 border rounded"
            placeholder="Your email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Button
            className="w-full"
            color="primary"
            disabled={status === "loading"}
            size="md"
            type="submit"
          >
            {status === "loading" ? "Submitting…" : "Join Waitlist"}
          </Button>
          {status === "success" && (
            <p className="text-green-600">Thanks! You’re on the list.</p>
          )}
          {status === "error" && (
            <p className="text-red-600">
              Oops—something went wrong. Please check your API credentials.
            </p>
          )}
        </form>
      </section>
    </div>
  );
}
