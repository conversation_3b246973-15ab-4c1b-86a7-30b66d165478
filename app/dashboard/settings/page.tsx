"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Chip } from "@heroui/chip";
import { Switch } from "@heroui/switch";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import { Avatar } from "@heroui/avatar";

interface UserProfile {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  roles: Role[];
  emailVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface NotificationSettings {
  emailNotifications: boolean;
  campaignUpdates: boolean;
  paymentAlerts: boolean;
  weeklyReports: boolean;
  securityAlerts: boolean;
}

export default function SettingsDashboard() {
  const { data: session, update: updateSession } = useSession();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    campaignUpdates: true,
    paymentAlerts: true,
    weeklyReports: false,
    securityAlerts: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form states
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");

  useEffect(() => {
    async function fetchUserProfile() {
      try {
        setLoading(true);
        setError(null);

        // In real implementation, fetch from API
        if (session?.user) {
          const mockProfile: UserProfile = {
            id: session.user.id,
            name: session.user.name || null,
            email: session.user.email || "",
            image: (session.user as any).image || null,
            roles: session.user.roles || [],
            emailVerified: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          setProfile(mockProfile);
          setName(mockProfile.name || "");
          setEmail(mockProfile.email);
        }
      } catch (err) {
        console.error("Failed to fetch user profile:", err);
        setError("Failed to load profile data");
      } finally {
        setLoading(false);
      }
    }

    fetchUserProfile();
  }, [session]);

  const handleSaveProfile = async () => {
    if (!profile) return;

    setSaving(true);
    try {
      // Mock save - in real implementation, call API
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update local state
      setProfile({
        ...profile,
        name,
        email,
        updatedAt: new Date(),
      });

      // Update session
      await updateSession({
        ...session,
        user: {
          ...session?.user,
          name,
          email,
        },
      });

      alert("Profile updated successfully!");
    } catch (err) {
      console.error("Failed to save profile:", err);
      alert("Failed to save profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleSaveNotifications = async () => {
    setSaving(true);
    try {
      // Mock save - in real implementation, call API
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert("Notification settings updated successfully!");
    } catch (err) {
      console.error("Failed to save notifications:", err);
      alert("Failed to save notification settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Account Settings</h1>
        <p className="text-default-500 mt-1">
          Manage your profile, preferences, and account security
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Profile Information</h2>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-6">
            {/* Avatar Section */}
            <div className="flex items-center gap-4">
              <Avatar
                color="primary"
                name={
                  profile?.name?.charAt(0).toUpperCase() ||
                  profile?.email?.charAt(0).toUpperCase()
                }
                size="lg"
                src={profile?.image || undefined}
              />
              <div>
                <p className="font-medium">{profile?.name || "No name set"}</p>
                <p className="text-sm text-default-500">{profile?.email}</p>
                <Button className="mt-2" size="sm" variant="flat">
                  Change Avatar
                </Button>
              </div>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                value={name}
                onValueChange={setName}
              />

              <Input
                label="Email Address"
                placeholder="Enter your email"
                type="email"
                value={email}
                onValueChange={setEmail}
              />

              <div className="flex items-center gap-2">
                <span className="text-sm">Email Verified:</span>
                <Chip
                  size="sm"
                  color={profile?.emailVerified ? "success" : "warning"}
                  variant="flat"
                >
                  {profile?.emailVerified ? "Verified" : "Not Verified"}
                </Chip>
                {!profile?.emailVerified && (
                  <Button size="sm" variant="flat" color="primary">
                    Verify Email
                  </Button>
                )}
              </div>
            </div>

            <Button
              color="primary"
              onPress={handleSaveProfile}
              isLoading={saving}
              className="w-full"
            >
              Save Profile
            </Button>
          </CardBody>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Account Information</h2>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-4">
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-default-700">User ID</p>
                <p className="text-sm text-default-500 font-mono">{profile?.id}</p>
              </div>

              <div>
                <p className="text-sm font-medium text-default-700">Roles</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {profile?.roles.map((role) => (
                    <Chip
                      key={role}
                      size="sm"
                      variant="flat"
                      color={role === Role.ADVERTISER ? "primary" : "secondary"}
                    >
                      {role === Role.ADVERTISER ? "Advertiser" : "Model Provider"}
                    </Chip>
                  ))}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-default-700">Member Since</p>
                <p className="text-sm text-default-500">
                  {profile?.createdAt ? formatDate(profile.createdAt) : "Unknown"}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-default-700">Last Updated</p>
                <p className="text-sm text-default-500">
                  {profile?.updatedAt ? formatDate(profile.updatedAt) : "Unknown"}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Notification Preferences</h2>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-default-500">Receive notifications via email</p>
              </div>
              <Switch
                isSelected={notifications.emailNotifications}
                onValueChange={(value) => setNotifications({...notifications, emailNotifications: value})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Campaign Updates</p>
                <p className="text-sm text-default-500">Get notified about campaign performance</p>
              </div>
              <Switch
                isSelected={notifications.campaignUpdates}
                onValueChange={(value) => setNotifications({...notifications, campaignUpdates: value})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Payment Alerts</p>
                <p className="text-sm text-default-500">Notifications for payments and billing</p>
              </div>
              <Switch
                isSelected={notifications.paymentAlerts}
                onValueChange={(value) => setNotifications({...notifications, paymentAlerts: value})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Weekly Reports</p>
                <p className="text-sm text-default-500">Receive weekly performance summaries</p>
              </div>
              <Switch
                isSelected={notifications.weeklyReports}
                onValueChange={(value) => setNotifications({...notifications, weeklyReports: value})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Security Alerts</p>
                <p className="text-sm text-default-500">Important security notifications</p>
              </div>
              <Switch
                isSelected={notifications.securityAlerts}
                onValueChange={(value) => setNotifications({...notifications, securityAlerts: value})}
              />
            </div>
          </div>

          <Button
            color="primary"
            onPress={handleSaveNotifications}
            isLoading={saving}
            className="w-full"
          >
            Save Notification Settings
          </Button>
        </CardBody>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Security & Privacy</h2>
        </CardHeader>
        <Divider />
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="flat" color="primary">
              Change Password
            </Button>
            <Button variant="flat" color="secondary">
              Two-Factor Authentication
            </Button>
            <Button variant="flat" color="warning">
              Download Data
            </Button>
            <Button variant="flat" color="danger">
              Delete Account
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}