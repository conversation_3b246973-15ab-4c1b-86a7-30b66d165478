import { ReactNode } from "react";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { DashboardLayout } from "@/components/dashboard/dashboard-layout";

interface DashboardLayoutProps {
  children: ReactNode;
}

export default async function Layout({ children }: DashboardLayoutProps) {
  const session = await getServerSession(authOptions);

  // Redirect to login if not authenticated
  if (!session) {
    redirect("/login");
  }

  // Redirect to setup if no roles assigned
  if (!session.user.roles || session.user.roles.length === 0) {
    redirect("/setup");
  }

  return <DashboardLayout>{children}</DashboardLayout>;
}
