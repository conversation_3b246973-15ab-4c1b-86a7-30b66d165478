"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Spinner } from "@heroui/spinner";
import { But<PERSON> } from "@heroui/button";
import NextLink from "next/link";

import { StatCard, ProgressCard, ActivityCard, QuickActionCard } from "@/components/dashboard/dashboard-cards";

interface DashboardStats {
  advertiser?: {
    totalCampaigns: number;
    totalBudget: number;
    totalSpend: number;
    totalImpressions: number;
    totalClicks: number;
    averageCTR: number;
  };
  modelProvider?: {
    totalApps: number;
    totalImpressions: number;
    totalClicks: number;
    totalRevenue: number;
  };
}

interface Activity {
  id: string;
  title: string;
  description: string;
  timestamp: Date;
  type: "campaign" | "app" | "payment" | "system";
}

export default function DashboardPage() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDashboardData() {
      if (!session?.user?.roles) return;

      try {
        setLoading(true);
        setError(null);

        const statsPromises: Promise<any>[] = [];

        // Fetch advertiser analytics if user has advertiser role
        if (session.user.roles.includes(Role.ADVERTISER)) {
          statsPromises.push(
            fetch("/api/analytics/advertiser")
              .then((res) => res.json())
              .then((data) => ({ advertiser: data }))
              .catch(() => ({ advertiser: null }))
          );
        }

        // Fetch model provider analytics if user has model provider role
        if (session.user.roles.includes(Role.MODEL_PROVIDER)) {
          statsPromises.push(
            fetch("/api/analytics/model")
              .then((res) => res.json())
              .then((data) => ({ modelProvider: data }))
              .catch(() => ({ modelProvider: null }))
          );
        }

        const results = await Promise.all(statsPromises);
        const combinedStats = results.reduce((acc, result) => ({ ...acc, ...result }), {});
        
        setStats(combinedStats);

        // Mock activities for now - in real implementation, fetch from API
        setActivities([
          {
            id: "1",
            title: "Campaign Created",
            description: "New advertising campaign 'Summer Sale' was created",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            type: "campaign",
          },
          {
            id: "2",
            title: "App Registered",
            description: "AI Chat Assistant app was successfully registered",
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
            type: "app",
          },
          {
            id: "3",
            title: "Payment Processed",
            description: "Budget deposit of $500 was processed",
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
            type: "payment",
          },
        ]);
      } catch (err) {
        console.error("Failed to fetch dashboard data:", err);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const userRoles = session?.user?.roles || [];
  const isAdvertiser = userRoles.includes(Role.ADVERTISER);
  const isModelProvider = userRoles.includes(Role.MODEL_PROVIDER);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-default-500 mt-1">
            Welcome back, {session?.user?.name || session?.user?.email}
          </p>
        </div>
        <div className="flex gap-2">
          {isAdvertiser && (
            <Button as={NextLink} href="/dashboard/advertiser" color="primary" variant="flat">
              Create Campaign
            </Button>
          )}
          {isModelProvider && (
            <Button as={NextLink} href="/dashboard/model-provider" color="secondary" variant="flat">
              Register App
            </Button>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Advertiser Stats */}
        {isAdvertiser && stats?.advertiser && (
          <>
            <StatCard
              title="Total Campaigns"
              value={stats.advertiser.totalCampaigns || 0}
              color="primary"
            />
            <StatCard
              title="Total Impressions"
              value={(stats.advertiser.totalImpressions || 0).toLocaleString()}
              color="success"
            />
            <StatCard
              title="Total Clicks"
              value={(stats.advertiser.totalClicks || 0).toLocaleString()}
              color="warning"
            />
            <StatCard
              title="Average CTR"
              value={`${(stats.advertiser.averageCTR || 0).toFixed(2)}%`}
              color="secondary"
            />
          </>
        )}

        {/* Model Provider Stats */}
        {isModelProvider && stats?.modelProvider && (
          <>
            <StatCard
              title="Total Apps"
              value={stats.modelProvider.totalApps || 0}
              color="secondary"
            />
            <StatCard
              title="Total Impressions"
              value={(stats.modelProvider.totalImpressions || 0).toLocaleString()}
              color="success"
            />
            <StatCard
              title="Total Clicks"
              value={(stats.modelProvider.totalClicks || 0).toLocaleString()}
              color="warning"
            />
            <StatCard
              title="Total Revenue"
              value={`$${(stats.modelProvider.totalRevenue || 0).toFixed(2)}`}
              color="primary"
            />
          </>
        )}

        {/* Default stats if no roles or no data */}
        {(!isAdvertiser && !isModelProvider) || (!stats?.advertiser && !stats?.modelProvider) ? (
          <>
            <StatCard title="Total Campaigns" value="0" color="primary" />
            <StatCard title="Total Apps" value="0" color="secondary" />
            <StatCard title="Total Impressions" value="0" color="success" />
            <StatCard title="Total Revenue" value="$0.00" color="warning" />
          </>
        ) : null}
      </div>

      {/* Budget Progress (Advertiser only) */}
      {isAdvertiser && stats?.advertiser && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ProgressCard
            title="Budget Usage"
            current={stats.advertiser.totalSpend || 0}
            total={stats.advertiser.totalBudget || 1}
            unit="$"
            color="primary"
          />
        </div>
      )}

      {/* Bottom Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <ActivityCard title="Recent Activity" activities={activities} />

        {/* Quick Actions */}
        <QuickActionCard
          title="Quick Actions"
          description="Common tasks and shortcuts"
          actions={[
            ...(isAdvertiser ? [
              { label: "Create Campaign", href: "/dashboard/advertiser", color: "primary" as const },
              { label: "View Analytics", href: "/dashboard/advertiser", color: "secondary" as const },
            ] : []),
            ...(isModelProvider ? [
              { label: "Register App", href: "/dashboard/model-provider", color: "secondary" as const },
              { label: "View Earnings", href: "/dashboard/model-provider", color: "success" as const },
            ] : []),
            { label: "Payment Settings", href: "/dashboard/payments", color: "warning" as const },
            { label: "Account Settings", href: "/dashboard/settings", color: "default" as const },
          ]}
        />
      </div>
    </div>
  );
}
