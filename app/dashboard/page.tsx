"use client";

import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Tabs, Tab } from "@heroui/tabs";
import { Card, CardBody } from "@heroui/card";
import { Spinner } from "@heroui/spinner";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import AdvertiserCampaignTab from "./advertiser/compaign/page";
import AdvertiserPaymentTab from "./advertiser/payment/page";
import ModelProviderAppTab from "./model-provider/app/page";
import ModelProviderPayoutTab from "./model-provider/payout/page";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedTab, setSelectedTab] = useState<string>("advertiser-campaign");

  const userRoles = session?.user?.roles || [];
  const isAdvertiser = userRoles.includes(Role.ADVERTISER);
  const isModelProvider = userRoles.includes(Role.MODEL_PROVIDER);

  // Set initial tab based on user roles and URL params
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam) {
      setSelectedTab(tabParam);
    } else {
      // Default to first available tab based on user roles
      if (isAdvertiser) {
        setSelectedTab("advertiser-campaign");
      } else if (isModelProvider) {
        setSelectedTab("model-provider-app");
      }
    }
  }, [searchParams, isAdvertiser, isModelProvider]);

  // Update URL when tab changes
  const handleTabChange = (key: string) => {
    setSelectedTab(key);
    const url = new URL(window.location.href);
    url.searchParams.set("tab", key);
    router.replace(url.pathname + url.search);
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!session) {
    return null; // This should be handled by the layout
  }

  // If user has no relevant roles, show message
  if (!isAdvertiser && !isModelProvider) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <h1 className="text-2xl font-bold text-foreground">Welcome to Dashboard</h1>
        <p className="text-default-500">
          Please contact support to get access to advertiser or model provider features.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
        <p className="text-default-500 mt-1">
          Manage your campaigns, apps, and account settings
        </p>
      </div>

      {/* Tabbed Interface */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={selectedTab}
            onSelectionChange={(key) => handleTabChange(key as string)}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-6 py-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            {/* Advertiser Section */}
            {isAdvertiser && (
              <>
                <Tab
                  key="advertiser-campaign"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>📢</span>
                      <span>Campaigns</span>
                    </div>
                  }
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <h2 className="text-xl font-semibold text-foreground">Advertiser - Campaigns</h2>
                      <p className="text-default-500">Manage your advertising campaigns and track performance</p>
                    </div>
                    <AdvertiserCampaignTab />
                  </div>
                </Tab>
                
                <Tab
                  key="advertiser-payment"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>💳</span>
                      <span>Payments</span>
                    </div>
                  }
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <h2 className="text-xl font-semibold text-foreground">Advertiser - Payments</h2>
                      <p className="text-default-500">Manage your payment methods and billing</p>
                    </div>
                    <AdvertiserPaymentTab />
                  </div>
                </Tab>
              </>
            )}

            {/* Model Provider Section */}
            {isModelProvider && (
              <>
                <Tab
                  key="model-provider-app"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>🤖</span>
                      <span>Apps</span>
                    </div>
                  }
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <h2 className="text-xl font-semibold text-foreground">Model Provider - Apps</h2>
                      <p className="text-default-500">Manage your AI applications and integrations</p>
                    </div>
                    <ModelProviderAppTab />
                  </div>
                </Tab>
                
                <Tab
                  key="model-provider-payout"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>💰</span>
                      <span>Payouts</span>
                    </div>
                  }
                >
                  <div className="p-6">
                    <div className="mb-4">
                      <h2 className="text-xl font-semibold text-foreground">Model Provider - Payouts</h2>
                      <p className="text-default-500">Manage your earnings and payout settings</p>
                    </div>
                    <ModelProviderPayoutTab />
                  </div>
                </Tab>
              </>
            )}
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
}
