"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/table";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface App {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  impressions: number;
  clicks: number;
  revenue: number;
  status: "active" | "inactive" | "pending";
}

interface ModelAnalytics {
  totalApps: number;
  totalImpressions: number;
  totalClicks: number;
  totalRevenue: number;
  topApps: App[];
}

export default function ModelProviderAppTab() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = useState<ModelAnalytics | null>(null);
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchModelProviderData() {
      if (!session?.user?.roles?.includes(Role.MODEL_PROVIDER)) {
        setError("Access denied. Model Provider role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Mock data for development
        const mockAnalytics: ModelAnalytics = {
          totalApps: 3,
          totalImpressions: 85000,
          totalClicks: 2100,
          totalRevenue: 1250.75,
          topApps: [],
        };

        const mockApps: App[] = [
          {
            id: "1",
            name: "AI Chat Assistant",
            description: "Intelligent conversational AI for customer support",
            createdAt: new Date("2024-06-01"),
            impressions: 35000,
            clicks: 875,
            revenue: 525.50,
            status: "active",
          },
          {
            id: "2",
            name: "Content Generator",
            description: "AI-powered content creation tool",
            createdAt: new Date("2024-05-15"),
            impressions: 28000,
            clicks: 700,
            revenue: 420.25,
            status: "active",
          },
          {
            id: "3",
            name: "Image Analyzer",
            description: "Computer vision API for image analysis",
            createdAt: new Date("2024-06-10"),
            impressions: 22000,
            clicks: 525,
            revenue: 305.00,
            status: "pending",
          },
        ];

        setAnalytics(mockAnalytics);
        setApps(mockApps);
      } catch (err) {
        console.error("Failed to fetch model provider data:", err);
        setError("Failed to load model provider data");
      } finally {
        setLoading(false);
      }
    }

    fetchModelProviderData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "inactive":
        return "default";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date));
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button color="secondary">
            Register New App
          </Button>
          <Button variant="flat">
            Integration Guide
          </Button>
        </div>
        <Button variant="flat" color="primary">
          API Documentation
        </Button>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Apps"
            value={analytics.totalApps}
            color="secondary"
          />
          <StatCard
            title="Total Revenue"
            value={formatCurrency(analytics.totalRevenue)}
            color="success"
          />
          <StatCard
            title="Total Impressions"
            value={analytics.totalImpressions.toLocaleString()}
            color="primary"
          />
          <StatCard
            title="Total Clicks"
            value={analytics.totalClicks.toLocaleString()}
            color="warning"
          />
        </div>
      )}

      {/* Apps Table */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Your Applications</h3>
          <Button size="sm" variant="flat">
            Filter
          </Button>
        </CardHeader>
        <Divider />
        <CardBody className="p-0">
          {apps.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-default-500 mb-4">No applications found</p>
              <Button color="secondary">
                Register Your First App
              </Button>
            </div>
          ) : (
            <Table aria-label="Apps table" removeWrapper>
              <TableHeader>
                <TableColumn>APPLICATION</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>REVENUE</TableColumn>
                <TableColumn>CTR</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {apps.map((app) => (
                  <TableRow key={app.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{app.name}</p>
                        <p className="text-sm text-default-500 truncate max-w-[200px]">
                          {app.description}
                        </p>
                        <p className="text-xs text-default-400">
                          Created {formatDate(app.createdAt)}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{app.impressions.toLocaleString()}</TableCell>
                    <TableCell>{app.clicks.toLocaleString()}</TableCell>
                    <TableCell>{formatCurrency(app.revenue)}</TableCell>
                    <TableCell>
                      {app.impressions > 0
                        ? `${((app.clicks / app.impressions) * 100).toFixed(2)}%`
                        : "0.00%"
                      }
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStatusColor(app.status)}
                      >
                        {app.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="flat"
                        >
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          color="secondary"
                        >
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Integration Guide */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Getting Started</h3>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="space-y-4">
            <div className="p-4 bg-default-50 rounded-lg">
              <h4 className="font-medium mb-2">1. Register Your Application</h4>
              <p className="text-sm text-default-600">
                Create a new app registration to get your API keys and start receiving ad impressions.
              </p>
            </div>

            <div className="p-4 bg-default-50 rounded-lg">
              <h4 className="font-medium mb-2">2. Integrate Ad SDK</h4>
              <p className="text-sm text-default-600">
                Use our SDK to display ads in your application and start earning revenue.
              </p>
            </div>

            <div className="p-4 bg-default-50 rounded-lg">
              <h4 className="font-medium mb-2">3. Monitor Performance</h4>
              <p className="text-sm text-default-600">
                Track your app's performance and earnings through this dashboard.
              </p>
            </div>
          </div>

          <div className="mt-6 flex gap-2">
            <Button color="secondary">
              View Documentation
            </Button>
            <Button variant="flat">
              Download SDK
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}