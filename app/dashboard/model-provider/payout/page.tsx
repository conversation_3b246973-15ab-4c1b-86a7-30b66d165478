"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { But<PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface StripeAccount {
  id: string;
  payouts_enabled: boolean;
  charges_enabled: boolean;
  details_submitted: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
  };
}

interface ModelProviderPayoutData {
  pendingBalance: number;
  totalEarnings: number;
  stripeAccount?: StripeAccount;
}

export default function ModelProviderPayoutTab() {
  const { data: session } = useSession();
  const [payoutData, setPayoutData] = useState<ModelProviderPayoutData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPayoutData() {
      if (!session?.user?.roles?.includes(Role.MODEL_PROVIDER)) {
        setError("Access denied. Model Provider role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Mock payout data
        const mockData: ModelProviderPayoutData = {
          pendingBalance: 125.50,
          totalEarnings: 1250.75,
          stripeAccount: {
            id: "acct_test",
            payouts_enabled: false,
            charges_enabled: false,
            details_submitted: false,
            requirements: {
              currently_due: ["individual.first_name", "individual.last_name"],
              eventually_due: ["individual.id_number"],
            },
          },
        };

        setPayoutData(mockData);
      } catch (err) {
        console.error("Failed to fetch payout data:", err);
        setError("Failed to load payout data");
      } finally {
        setLoading(false);
      }
    }

    fetchPayoutData();
  }, [session]);

  const handleStripeConnect = async () => {
    try {
      const response = await fetch("/api/stripe/connect/onboard", {
        method: "POST",
      });

      if (response.ok) {
        const { accountLinkUrl } = await response.json();
        window.location.href = accountLinkUrl;
      } else {
        throw new Error("Failed to create onboarding link");
      }
    } catch (err) {
      console.error("Failed to start Stripe Connect onboarding:", err);
      alert("Failed to start onboarding. Please try again.");
    }
  };

  const handleOpenStripeDashboard = async () => {
    try {
      const response = await fetch("/api/stripe/connect/dashboard", {
        method: "POST",
      });

      if (response.ok) {
        const { dashboardUrl } = await response.json();
        window.open(dashboardUrl, "_blank");
      } else {
        throw new Error("Failed to create dashboard link");
      }
    } catch (err) {
      console.error("Failed to open Stripe dashboard:", err);
      alert("Failed to open dashboard. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Balance Overview */}
      {payoutData && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <StatCard
            title="Pending Earnings"
            value={formatCurrency(payoutData.pendingBalance)}
            color="warning"
          />
          <StatCard
            title="Total Earnings"
            value={formatCurrency(payoutData.totalEarnings)}
            color="success"
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payout Setup */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Payout Setup</h3>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-4">
            {payoutData?.stripeAccount ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Account Status:</span>
                  <Chip
                    size="sm"
                    color={payoutData.stripeAccount.payouts_enabled ? "success" : "warning"}
                    variant="flat"
                  >
                    {payoutData.stripeAccount.payouts_enabled ? "Active" : "Setup Required"}
                  </Chip>
                </div>

                {!payoutData.stripeAccount.payouts_enabled && (
                  <div className="p-4 bg-warning-50 rounded-lg">
                    <p className="text-sm text-warning-700 mb-2">
                      Complete your account setup to receive payouts:
                    </p>
                    <ul className="text-xs text-warning-600 space-y-1">
                      {payoutData.stripeAccount.requirements.currently_due.map((req) => (
                        <li key={req}>• {req.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    color="primary"
                    onPress={handleOpenStripeDashboard}
                    className="flex-1"
                  >
                    Manage Account
                  </Button>
                  {!payoutData.stripeAccount.payouts_enabled && (
                    <Button
                      color="secondary"
                      variant="flat"
                      onPress={handleStripeConnect}
                      className="flex-1"
                    >
                      Complete Setup
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-default-500">
                  Set up your Stripe Connect account to receive earnings from your AI applications.
                </p>

                <Button
                  color="secondary"
                  onPress={handleStripeConnect}
                  className="w-full"
                >
                  Setup Payout Account
                </Button>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Payout Schedule */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Payout Schedule</h3>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-default-600">Payout Frequency</span>
                <span className="font-medium">Weekly</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-default-600">Next Payout</span>
                <span className="font-medium">July 8, 2024</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-default-600">Minimum Payout</span>
                <span className="font-medium">$25.00</span>
              </div>
            </div>

            <Divider />

            <div className="space-y-2">
              <h4 className="font-medium">Payout Methods</h4>
              <p className="text-sm text-default-500">
                Payouts are processed through Stripe Connect to your connected bank account.
              </p>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Earnings History */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Recent Earnings</h3>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">AI Chat Assistant</p>
                <p className="text-sm text-default-500">June 25, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-success">+$45.25</p>
                <p className="text-sm text-default-500">125 clicks</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">Content Generator</p>
                <p className="text-sm text-default-500">June 24, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-success">+$32.50</p>
                <p className="text-sm text-default-500">98 clicks</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">Image Analyzer</p>
                <p className="text-sm text-default-500">June 23, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-success">+$28.75</p>
                <p className="text-sm text-default-500">87 clicks</p>
              </div>
            </div>
          </div>

          <div className="mt-4 text-center">
            <Button variant="flat" size="sm">
              View All Earnings
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}