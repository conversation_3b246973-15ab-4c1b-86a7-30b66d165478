"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/table";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import NextLink from "next/link";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface App {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  impressions: number;
  clicks: number;
  revenue: number;
  status: "active" | "inactive" | "pending";
}

interface ModelAnalytics {
  totalApps: number;
  totalImpressions: number;
  totalClicks: number;
  totalRevenue: number;
  topApps: App[];
}

export default function ModelProviderDashboard() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = useState<ModelAnalytics | null>(null);
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchModelProviderData() {
      if (!session?.user?.roles?.includes(Role.MODEL_PROVIDER)) {
        setError("Access denied. Model Provider role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch analytics
        const analyticsResponse = await fetch("/api/analytics/model");
        if (analyticsResponse.ok) {
          const analyticsData = await analyticsResponse.json();
          setAnalytics(analyticsData);
        }

        // Fetch apps
        const appsResponse = await fetch("/api/apps");
        if (appsResponse.ok) {
          const appsData = await appsResponse.json();
          setApps(appsData.apps || []);
        }
      } catch (err) {
        console.error("Failed to fetch model provider data:", err);
        setError("Failed to load model provider data");
      } finally {
        setLoading(false);
      }
    }

    fetchModelProviderData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "inactive":
        return "default";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Model Provider Dashboard</h1>
          <p className="text-default-500 mt-1">
            Manage your AI applications and track earnings
          </p>
        </div>
        <Button as={NextLink} href="/dashboard/model-provider/create" color="secondary">
          Register App
        </Button>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Apps"
            value={analytics.totalApps}
            color="secondary"
          />
          <StatCard
            title="Total Revenue"
            value={formatCurrency(analytics.totalRevenue)}
            color="success"
          />
          <StatCard
            title="Total Impressions"
            value={analytics.totalImpressions.toLocaleString()}
            color="primary"
          />
          <StatCard
            title="Total Clicks"
            value={analytics.totalClicks.toLocaleString()}
            color="warning"
          />
        </div>
      )}

      {/* Performance Overview */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Revenue Overview</h3>
            </CardHeader>
            <Divider />
            <CardBody>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-default-500">Total Earned</span>
                  <span className="text-2xl font-bold text-success">
                    {formatCurrency(analytics.totalRevenue)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-default-500">Average per Click</span>
                  <span className="font-medium">
                    {analytics.totalClicks > 0 
                      ? formatCurrency(analytics.totalRevenue / analytics.totalClicks)
                      : "$0.00"
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-default-500">Click-through Rate</span>
                  <span className="font-medium">
                    {analytics.totalImpressions > 0 
                      ? `${((analytics.totalClicks / analytics.totalImpressions) * 100).toFixed(2)}%`
                      : "0.00%"
                    }
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Quick Actions</h3>
            </CardHeader>
            <Divider />
            <CardBody>
              <div className="space-y-3">
                <Button 
                  as={NextLink} 
                  href="/dashboard/model-provider/create" 
                  color="secondary" 
                  className="w-full"
                >
                  Register New App
                </Button>
                <Button 
                  as={NextLink} 
                  href="/dashboard/payments" 
                  color="success" 
                  variant="flat"
                  className="w-full"
                >
                  Setup Payouts
                </Button>
                <Button 
                  as={NextLink} 
                  href="/dashboard/model-provider/integration" 
                  color="primary" 
                  variant="flat"
                  className="w-full"
                >
                  Integration Guide
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Apps Table */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Your Applications</h2>
          <Button as={NextLink} href="/dashboard/model-provider/create" size="sm" variant="flat">
            Register App
          </Button>
        </CardHeader>
        <Divider />
        <CardBody className="p-0">
          {apps.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-default-500 mb-4">No applications found</p>
              <Button as={NextLink} href="/dashboard/model-provider/create" color="secondary">
                Register Your First App
              </Button>
            </div>
          ) : (
            <Table aria-label="Apps table" removeWrapper>
              <TableHeader>
                <TableColumn>APPLICATION</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>REVENUE</TableColumn>
                <TableColumn>CTR</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {apps.map((app) => (
                  <TableRow key={app.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{app.name}</p>
                        <p className="text-sm text-default-500 truncate max-w-[200px]">
                          {app.description}
                        </p>
                        <p className="text-xs text-default-400">
                          Created {formatDate(app.createdAt)}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{app.impressions.toLocaleString()}</TableCell>
                    <TableCell>{app.clicks.toLocaleString()}</TableCell>
                    <TableCell>{formatCurrency(app.revenue)}</TableCell>
                    <TableCell>
                      {app.impressions > 0 
                        ? `${((app.clicks / app.impressions) * 100).toFixed(2)}%`
                        : "0.00%"
                      }
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStatusColor(app.status)}
                      >
                        {app.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          as={NextLink}
                          href={`/dashboard/model-provider/apps/${app.id}`}
                          size="sm"
                          variant="flat"
                        >
                          View
                        </Button>
                        <Button
                          as={NextLink}
                          href={`/dashboard/model-provider/apps/${app.id}/edit`}
                          size="sm"
                          variant="flat"
                          color="secondary"
                        >
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Top Performing Apps */}
      {analytics?.topApps && analytics.topApps.length > 0 && (
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Top Performing Applications</h2>
          </CardHeader>
          <Divider />
          <CardBody>
            <div className="space-y-4">
              {analytics.topApps.slice(0, 5).map((app) => (
                <div key={app.id} className="flex items-center justify-between p-4 bg-default-50 rounded-lg">
                  <div>
                    <p className="font-medium">{app.name}</p>
                    <p className="text-sm text-default-500">
                      {app.impressions.toLocaleString()} impressions • {app.clicks.toLocaleString()} clicks
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-success">{formatCurrency(app.revenue)}</p>
                    <p className="text-sm text-default-500">
                      {app.impressions > 0 
                        ? `${((app.clicks / app.impressions) * 100).toFixed(2)}% CTR`
                        : "0.00% CTR"
                      }
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
