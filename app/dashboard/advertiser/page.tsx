"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/table";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import NextLink from "next/link";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface Campaign {
  id: string;
  name: string;
  budget: number;
  bidType: string;
  bidAmount: number;
  createdAt: Date;
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
  status: "active" | "paused" | "completed";
}

interface AdvertiserAnalytics {
  totalCampaigns: number;
  totalBudget: number;
  totalSpend: number;
  totalImpressions: number;
  totalClicks: number;
  averageCTR: number;
  topCampaigns: Campaign[];
}

export default function AdvertiserDashboard() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = useState<AdvertiserAnalytics | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAdvertiserData() {
      if (!session?.user?.roles?.includes(Role.ADVERTISER)) {
        setError("Access denied. Advertiser role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch analytics
        const analyticsResponse = await fetch("/api/analytics/advertiser");
        if (analyticsResponse.ok) {
          const analyticsData = await analyticsResponse.json();
          setAnalytics(analyticsData);
        }

        // Fetch campaigns
        const campaignsResponse = await fetch("/api/ads");
        if (campaignsResponse.ok) {
          const campaignsData = await campaignsResponse.json();
          setCampaigns(campaignsData.ads || []);
        }
      } catch (err) {
        console.error("Failed to fetch advertiser data:", err);
        setError("Failed to load advertiser data");
      } finally {
        setLoading(false);
      }
    }

    fetchAdvertiserData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "paused":
        return "warning";
      case "completed":
        return "default";
      default:
        return "default";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Advertiser Dashboard</h1>
          <p className="text-default-500 mt-1">
            Manage your advertising campaigns and track performance
          </p>
        </div>
        <Button as={NextLink} href="/dashboard/advertiser/create" color="primary">
          Create Campaign
        </Button>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Campaigns"
            value={analytics.totalCampaigns}
            color="primary"
          />
          <StatCard
            title="Total Budget"
            value={formatCurrency(analytics.totalBudget)}
            color="secondary"
          />
          <StatCard
            title="Total Spend"
            value={formatCurrency(analytics.totalSpend)}
            subtitle={`${((analytics.totalSpend / analytics.totalBudget) * 100).toFixed(1)}% of budget`}
            color="warning"
          />
          <StatCard
            title="Average CTR"
            value={`${analytics.averageCTR.toFixed(2)}%`}
            color="success"
          />
        </div>
      )}

      {/* Performance Overview */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <StatCard
            title="Total Impressions"
            value={analytics.totalImpressions.toLocaleString()}
            color="success"
          />
          <StatCard
            title="Total Clicks"
            value={analytics.totalClicks.toLocaleString()}
            color="primary"
          />
        </div>
      )}

      {/* Campaigns Table */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Your Campaigns</h2>
          <Button as={NextLink} href="/dashboard/advertiser/create" size="sm" variant="flat">
            New Campaign
          </Button>
        </CardHeader>
        <Divider />
        <CardBody className="p-0">
          {campaigns.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-default-500 mb-4">No campaigns found</p>
              <Button as={NextLink} href="/dashboard/advertiser/create" color="primary">
                Create Your First Campaign
              </Button>
            </div>
          ) : (
            <Table aria-label="Campaigns table" removeWrapper>
              <TableHeader>
                <TableColumn>CAMPAIGN</TableColumn>
                <TableColumn>BUDGET</TableColumn>
                <TableColumn>SPEND</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>CTR</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {campaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{campaign.name}</p>
                        <p className="text-sm text-default-500">
                          Created {formatDate(campaign.createdAt)}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(campaign.budget)}</TableCell>
                    <TableCell>{formatCurrency(campaign.spend)}</TableCell>
                    <TableCell>{campaign.impressions.toLocaleString()}</TableCell>
                    <TableCell>{campaign.clicks.toLocaleString()}</TableCell>
                    <TableCell>{campaign.ctr.toFixed(2)}%</TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStatusColor(campaign.status)}
                      >
                        {campaign.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          as={NextLink}
                          href={`/dashboard/advertiser/campaigns/${campaign.id}`}
                          size="sm"
                          variant="flat"
                        >
                          View
                        </Button>
                        <Button
                          as={NextLink}
                          href={`/dashboard/advertiser/campaigns/${campaign.id}/edit`}
                          size="sm"
                          variant="flat"
                          color="secondary"
                        >
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Top Performing Campaigns */}
      {analytics?.topCampaigns && analytics.topCampaigns.length > 0 && (
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Top Performing Campaigns</h2>
          </CardHeader>
          <Divider />
          <CardBody>
            <div className="space-y-4">
              {analytics.topCampaigns.slice(0, 5).map((campaign) => (
                <div key={campaign.id} className="flex items-center justify-between p-4 bg-default-50 rounded-lg">
                  <div>
                    <p className="font-medium">{campaign.name}</p>
                    <p className="text-sm text-default-500">
                      {campaign.impressions.toLocaleString()} impressions • {campaign.clicks.toLocaleString()} clicks
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(campaign.spend)}</p>
                    <p className="text-sm text-success">{campaign.ctr.toFixed(2)}% CTR</p>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
