"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { But<PERSON> } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/table";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import NextLink from "next/link";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface Campaign {
  id: string;
  name: string;
  budget: number;
  bidType: string;
  bidAmount: number;
  createdAt: Date;
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
  status: "active" | "paused" | "completed";
}

interface AdvertiserAnalytics {
  totalCampaigns: number;
  totalBudget: number;
  totalSpend: number;
  totalImpressions: number;
  totalClicks: number;
  averageCTR: number;
  topCampaigns: Campaign[];
}

export default function AdvertiserCampaignTab() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = useState<AdvertiserAnalytics | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAdvertiserData() {
      if (!session?.user?.roles?.includes(Role.ADVERTISER)) {
        setError("Access denied. Advertiser role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Mock data for development
        const mockAnalytics: AdvertiserAnalytics = {
          totalCampaigns: 5,
          totalBudget: 5000,
          totalSpend: 2750,
          totalImpressions: 125000,
          totalClicks: 3250,
          averageCTR: 2.6,
          topCampaigns: [],
        };

        const mockCampaigns: Campaign[] = [
          {
            id: "1",
            name: "Summer Sale Campaign",
            budget: 1000,
            bidType: "CPC",
            bidAmount: 0.50,
            createdAt: new Date("2024-06-15"),
            impressions: 25000,
            clicks: 750,
            spend: 375,
            ctr: 3.0,
            status: "active",
          },
          {
            id: "2",
            name: "Product Launch",
            budget: 2000,
            bidType: "CPM",
            bidAmount: 2.00,
            createdAt: new Date("2024-06-10"),
            impressions: 50000,
            clicks: 1200,
            spend: 600,
            ctr: 2.4,
            status: "active",
          },
          {
            id: "3",
            name: "Brand Awareness",
            budget: 1500,
            bidType: "CPC",
            bidAmount: 0.75,
            createdAt: new Date("2024-06-01"),
            impressions: 30000,
            clicks: 900,
            spend: 675,
            ctr: 3.0,
            status: "paused",
          },
        ];

        setAnalytics(mockAnalytics);
        setCampaigns(mockCampaigns);
      } catch (err) {
        console.error("Failed to fetch advertiser data:", err);
        setError("Failed to load advertiser data");
      } finally {
        setLoading(false);
      }
    }

    fetchAdvertiserData();
  }, [session]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "paused":
        return "warning";
      case "completed":
        return "default";
      default:
        return "default";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(date));
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button color="primary">
            Create Campaign
          </Button>
          <Button variant="flat">
            Import Campaigns
          </Button>
        </div>
        <Button variant="flat" color="secondary">
          Export Data
        </Button>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Campaigns"
            value={analytics.totalCampaigns}
            color="primary"
          />
          <StatCard
            title="Total Budget"
            value={formatCurrency(analytics.totalBudget)}
            color="secondary"
          />
          <StatCard
            title="Total Spend"
            value={formatCurrency(analytics.totalSpend)}
            subtitle={`${((analytics.totalSpend / analytics.totalBudget) * 100).toFixed(1)}% of budget`}
            color="warning"
          />
          <StatCard
            title="Average CTR"
            value={`${analytics.averageCTR.toFixed(2)}%`}
            color="success"
          />
        </div>
      )}

      {/* Campaigns Table */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Your Campaigns</h3>
          <Button size="sm" variant="flat">
            Filter
          </Button>
        </CardHeader>
        <Divider />
        <CardBody className="p-0">
          {campaigns.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-default-500 mb-4">No campaigns found</p>
              <Button color="primary">
                Create Your First Campaign
              </Button>
            </div>
          ) : (
            <Table aria-label="Campaigns table" removeWrapper>
              <TableHeader>
                <TableColumn>CAMPAIGN</TableColumn>
                <TableColumn>BUDGET</TableColumn>
                <TableColumn>SPEND</TableColumn>
                <TableColumn>IMPRESSIONS</TableColumn>
                <TableColumn>CLICKS</TableColumn>
                <TableColumn>CTR</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {campaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{campaign.name}</p>
                        <p className="text-sm text-default-500">
                          Created {formatDate(campaign.createdAt)}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(campaign.budget)}</TableCell>
                    <TableCell>{formatCurrency(campaign.spend)}</TableCell>
                    <TableCell>{campaign.impressions.toLocaleString()}</TableCell>
                    <TableCell>{campaign.clicks.toLocaleString()}</TableCell>
                    <TableCell>{campaign.ctr.toFixed(2)}%</TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStatusColor(campaign.status)}
                      >
                        {campaign.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="flat"
                        >
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="flat"
                          color="secondary"
                        >
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>
    </div>
  );
}