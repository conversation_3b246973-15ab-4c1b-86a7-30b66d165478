"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface PaymentMethod {
  id: string;
  type: "card" | "bank_account";
  last4: string;
  brand?: string;
  isDefault: boolean;
}

interface AdvertiserPaymentData {
  balance: number;
  totalSpent: number;
  paymentMethods: PaymentMethod[];
}

export default function AdvertiserPaymentTab() {
  const { data: session } = useSession();
  const [paymentData, setPaymentData] = useState<AdvertiserPaymentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [depositAmount, setDepositAmount] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    async function fetchPaymentData() {
      if (!session?.user?.roles?.includes(Role.ADVERTISER)) {
        setError("Access denied. Advertiser role required.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Mock payment data
        const mockData: AdvertiserPaymentData = {
          balance: 250.00,
          totalSpent: 750.25,
          paymentMethods: [
            {
              id: "pm_1",
              type: "card",
              last4: "4242",
              brand: "visa",
              isDefault: true,
            },
            {
              id: "pm_2",
              type: "card",
              last4: "0005",
              brand: "mastercard",
              isDefault: false,
            },
          ],
        };

        setPaymentData(mockData);
      } catch (err) {
        console.error("Failed to fetch payment data:", err);
        setError("Failed to load payment data");
      } finally {
        setLoading(false);
      }
    }

    fetchPaymentData();
  }, [session]);

  const handleDepositFunds = async () => {
    if (!depositAmount || parseFloat(depositAmount) <= 0) return;

    setIsProcessing(true);
    try {
      // Mock deposit process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Update balance
      if (paymentData) {
        setPaymentData({
          ...paymentData,
          balance: paymentData.balance + parseFloat(depositAmount),
        });
      }

      setDepositAmount("");
      alert("Funds deposited successfully!");
    } catch (err) {
      console.error("Failed to deposit funds:", err);
      alert("Failed to deposit funds. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Balance Overview */}
      {paymentData && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <StatCard
            title="Account Balance"
            value={formatCurrency(paymentData.balance)}
            color="primary"
          />
          <StatCard
            title="Total Spent"
            value={formatCurrency(paymentData.totalSpent)}
            color="warning"
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Add Funds */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Add Funds</h3>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-4">
            <p className="text-default-500">
              Deposit funds to your account to run advertising campaigns.
            </p>

            <div className="space-y-4">
              <Input
                type="number"
                label="Deposit Amount"
                placeholder="0.00"
                value={depositAmount}
                onValueChange={setDepositAmount}
                startContent={
                  <div className="pointer-events-none flex items-center">
                    <span className="text-default-400 text-small">$</span>
                  </div>
                }
              />

              <Select
                label="Payment Method"
                placeholder="Select payment method"
                defaultSelectedKeys={["pm_1"]}
              >
                {paymentData?.paymentMethods.map((method) => (
                  <SelectItem key={method.id}>
                    {method.type === "card"
                      ? `${method.brand?.toUpperCase()} •••• ${method.last4}`
                      : `Bank •••• ${method.last4}`
                    }
                    {method.isDefault && " (Default)"}
                  </SelectItem>
                )) || []}
              </Select>

              <Button
                color="primary"
                onPress={handleDepositFunds}
                isLoading={isProcessing}
                isDisabled={!depositAmount || parseFloat(depositAmount) <= 0}
                className="w-full"
              >
                Deposit Funds
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Payment Methods */}
        <Card>
          <CardHeader className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Payment Methods</h3>
            <Button size="sm" variant="flat">
              Add Method
            </Button>
          </CardHeader>
          <Divider />
          <CardBody>
            {paymentData?.paymentMethods.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-default-500 mb-4">No payment methods found</p>
                <Button color="primary">Add Payment Method</Button>
              </div>
            ) : (
              <div className="space-y-3">
                {paymentData?.paymentMethods.map((method) => (
                  <div key={method.id} className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">
                          {method.type === "card" ? "💳" : "🏦"}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">
                          {method.type === "card"
                            ? `${method.brand?.toUpperCase()} •••• ${method.last4}`
                            : `Bank Account •••• ${method.last4}`
                          }
                        </p>
                        {method.isDefault && (
                          <Chip size="sm" color="primary" variant="flat">
                            Default
                          </Chip>
                        )}
                      </div>
                    </div>
                    <Button size="sm" variant="flat">
                      Edit
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Recent Transactions</h3>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">Campaign Spend - Summer Sale</p>
                <p className="text-sm text-default-500">June 25, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-danger">-$125.50</p>
                <p className="text-sm text-default-500">Campaign</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">Account Deposit</p>
                <p className="text-sm text-default-500">June 20, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-success">+$500.00</p>
                <p className="text-sm text-default-500">Deposit</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium">Campaign Spend - Product Launch</p>
                <p className="text-sm text-default-500">June 18, 2024</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-danger">-$89.25</p>
                <p className="text-sm text-default-500">Campaign</p>
              </div>
            </div>
          </div>

          <div className="mt-4 text-center">
            <Button variant="flat" size="sm">
              View All Transactions
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}