"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Chip } from "@heroui/chip";
import { Spinner } from "@heroui/spinner";
import { Divider } from "@heroui/divider";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";

import { StatCard } from "@/components/dashboard/dashboard-cards";

interface PaymentMethod {
  id: string;
  type: "card" | "bank_account";
  last4: string;
  brand?: string;
  isDefault: boolean;
}

interface StripeAccount {
  id: string;
  payouts_enabled: boolean;
  charges_enabled: boolean;
  details_submitted: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
  };
}

interface PaymentData {
  balance: number;
  pendingBalance: number;
  totalEarnings: number;
  totalSpent: number;
  paymentMethods: PaymentMethod[];
  stripeAccount?: StripeAccount;
}

export default function PaymentsDashboard() {
  const { data: session } = useSession();
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [depositAmount, setDepositAmount] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const userRoles = session?.user?.roles || [];
  const isAdvertiser = userRoles.includes(Role.ADVERTISER);
  const isModelProvider = userRoles.includes(Role.MODEL_PROVIDER);

  useEffect(() => {
    async function fetchPaymentData() {
      try {
        setLoading(true);
        setError(null);

        // Mock payment data - in real implementation, fetch from API
        const mockData: PaymentData = {
          balance: isAdvertiser ? 250.00 : 0,
          pendingBalance: isModelProvider ? 125.50 : 0,
          totalEarnings: isModelProvider ? 1250.75 : 0,
          totalSpent: isAdvertiser ? 750.25 : 0,
          paymentMethods: [
            {
              id: "pm_1",
              type: "card",
              last4: "4242",
              brand: "visa",
              isDefault: true,
            },
          ],
          stripeAccount: isModelProvider ? {
            id: "acct_test",
            payouts_enabled: false,
            charges_enabled: false,
            details_submitted: false,
            requirements: {
              currently_due: ["individual.first_name", "individual.last_name"],
              eventually_due: ["individual.id_number"],
            },
          } : undefined,
        };

        setPaymentData(mockData);
      } catch (err) {
        console.error("Failed to fetch payment data:", err);
        setError("Failed to load payment data");
      } finally {
        setLoading(false);
      }
    }

    fetchPaymentData();
  }, [session, isAdvertiser, isModelProvider]);

  const handleDepositFunds = async () => {
    if (!depositAmount || parseFloat(depositAmount) <= 0) return;

    setIsProcessing(true);
    try {
      // Mock deposit process - in real implementation, integrate with Stripe
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update balance
      if (paymentData) {
        setPaymentData({
          ...paymentData,
          balance: paymentData.balance + parseFloat(depositAmount),
        });
      }
      
      setDepositAmount("");
      alert("Funds deposited successfully!");
    } catch (err) {
      console.error("Failed to deposit funds:", err);
      alert("Failed to deposit funds. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStripeConnect = async () => {
    try {
      const response = await fetch("/api/stripe/connect/onboard", {
        method: "POST",
      });
      
      if (response.ok) {
        const { accountLinkUrl } = await response.json();
        window.location.href = accountLinkUrl;
      } else {
        throw new Error("Failed to create onboarding link");
      }
    } catch (err) {
      console.error("Failed to start Stripe Connect onboarding:", err);
      alert("Failed to start onboarding. Please try again.");
    }
  };

  const handleOpenStripeDashboard = async () => {
    try {
      const response = await fetch("/api/stripe/connect/dashboard", {
        method: "POST",
      });
      
      if (response.ok) {
        const { dashboardUrl } = await response.json();
        window.open(dashboardUrl, "_blank");
      } else {
        throw new Error("Failed to create dashboard link");
      }
    } catch (err) {
      console.error("Failed to open Stripe dashboard:", err);
      alert("Failed to open dashboard. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-danger">{error}</p>
        <Button color="primary" onPress={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Payment Management</h1>
        <p className="text-default-500 mt-1">
          Manage your payment methods, deposits, and earnings
        </p>
      </div>

      {/* Balance Overview */}
      {paymentData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {isAdvertiser && (
            <>
              <StatCard
                title="Account Balance"
                value={formatCurrency(paymentData.balance)}
                color="primary"
              />
              <StatCard
                title="Total Spent"
                value={formatCurrency(paymentData.totalSpent)}
                color="warning"
              />
            </>
          )}
          {isModelProvider && (
            <>
              <StatCard
                title="Pending Earnings"
                value={formatCurrency(paymentData.pendingBalance)}
                color="warning"
              />
              <StatCard
                title="Total Earnings"
                value={formatCurrency(paymentData.totalEarnings)}
                color="success"
              />
            </>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Advertiser Payment Section */}
        {isAdvertiser && (
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Add Funds</h2>
            </CardHeader>
            <Divider />
            <CardBody className="space-y-4">
              <p className="text-default-500">
                Deposit funds to your account to run advertising campaigns.
              </p>
              
              <div className="space-y-4">
                <Input
                  type="number"
                  label="Deposit Amount"
                  placeholder="0.00"
                  value={depositAmount}
                  onValueChange={setDepositAmount}
                  startContent={
                    <div className="pointer-events-none flex items-center">
                      <span className="text-default-400 text-small">$</span>
                    </div>
                  }
                />
                
                <Select
                  label="Payment Method"
                  placeholder="Select payment method"
                  defaultSelectedKeys={["pm_1"]}
                >
                  {paymentData?.paymentMethods.map((method) => (
                    <SelectItem key={method.id}>
                      {method.type === "card"
                        ? `${method.brand?.toUpperCase()} •••• ${method.last4}`
                        : `Bank •••• ${method.last4}`
                      }
                      {method.isDefault && " (Default)"}
                    </SelectItem>
                  )) || []}
                </Select>
                
                <Button
                  color="primary"
                  onPress={handleDepositFunds}
                  isLoading={isProcessing}
                  isDisabled={!depositAmount || parseFloat(depositAmount) <= 0}
                  className="w-full"
                >
                  Deposit Funds
                </Button>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Model Provider Payout Section */}
        {isModelProvider && (
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Payout Setup</h2>
            </CardHeader>
            <Divider />
            <CardBody className="space-y-4">
              {paymentData?.stripeAccount ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Account Status:</span>
                    <Chip
                      size="sm"
                      color={paymentData.stripeAccount.payouts_enabled ? "success" : "warning"}
                      variant="flat"
                    >
                      {paymentData.stripeAccount.payouts_enabled ? "Active" : "Setup Required"}
                    </Chip>
                  </div>
                  
                  {!paymentData.stripeAccount.payouts_enabled && (
                    <div className="p-4 bg-warning-50 rounded-lg">
                      <p className="text-sm text-warning-700 mb-2">
                        Complete your account setup to receive payouts:
                      </p>
                      <ul className="text-xs text-warning-600 space-y-1">
                        {paymentData.stripeAccount.requirements.currently_due.map((req) => (
                          <li key={req}>• {req.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button
                      color="primary"
                      onPress={handleOpenStripeDashboard}
                      className="flex-1"
                    >
                      Manage Account
                    </Button>
                    {!paymentData.stripeAccount.payouts_enabled && (
                      <Button
                        color="secondary"
                        variant="flat"
                        onPress={handleStripeConnect}
                        className="flex-1"
                      >
                        Complete Setup
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-default-500">
                    Set up your Stripe Connect account to receive earnings from your AI applications.
                  </p>
                  
                  <Button
                    color="secondary"
                    onPress={handleStripeConnect}
                    className="w-full"
                  >
                    Setup Payout Account
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        )}

        {/* Payment Methods */}
        <Card>
          <CardHeader className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Payment Methods</h2>
            <Button size="sm" variant="flat">
              Add Method
            </Button>
          </CardHeader>
          <Divider />
          <CardBody>
            {paymentData?.paymentMethods.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-default-500 mb-4">No payment methods found</p>
                <Button color="primary">Add Payment Method</Button>
              </div>
            ) : (
              <div className="space-y-3">
                {paymentData?.paymentMethods.map((method) => (
                  <div key={method.id} className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">
                          {method.type === "card" ? "💳" : "🏦"}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">
                          {method.type === "card" 
                            ? `${method.brand?.toUpperCase()} •••• ${method.last4}`
                            : `Bank Account •••• ${method.last4}`
                          }
                        </p>
                        {method.isDefault && (
                          <Chip size="sm" color="primary" variant="flat">
                            Default
                          </Chip>
                        )}
                      </div>
                    </div>
                    <Button size="sm" variant="flat">
                      Edit
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
