import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { AppService } from "@/lib/services/app";
import {
  withValidationAPIErrorHandling,
  withAPIErrorHandling,
} from "@/lib/error-handler";

// App registration validation
const appSchema = z.object({
  name: z
    .string({ error: "App name is required" })
    .min(3, "App name must be at least 3 characters long")
    .max(100, "App name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

async function getAppsHandler(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  // Middleware handles authentication and role checking, but we still need session for user ID
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Use AppService to get user apps
  const apps = await AppService.getUserApps(session.user.id);

  return NextResponse.json({ apps });
}

export const GET = withAPIErrorHandling(getAppsHandler);

async function createAppHandler(
  request: NextRequest,
  validatedData: z.infer<typeof appSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  // Middleware handles authentication and role checking, but we still need session for user ID
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { name, description } = validatedData;

  // Use AppService to create app
  const app = await AppService.createApp(session.user.id, name, description);

  return NextResponse.json(
    {
      message: "Application created successfully",
      app,
    },
    { status: 201 },
  );
}

// Apply error handling middleware - authentication handled by middleware
export const POST = withValidationAPIErrorHandling(createAppHandler, appSchema);
