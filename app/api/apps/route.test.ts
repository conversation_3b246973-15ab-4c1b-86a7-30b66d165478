import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { AppStatus, Prisma } from "@prisma/client";

import { GET, POST } from "./route";

import { AppService } from "@/lib/services/app";
import { stringifyDates } from "@/lib/testUtils";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/services/app", () => ({
  AppService: {
    getUserApps: vi.fn(),
    createApp: vi.fn(),
  },
}));

describe("/api/apps", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/apps", () => {
    const mockApps = [
      {
        id: "app-1",
        name: "Test App 1",
        appId: "app_1234567890123456",
        appSecret: "secret_1234567890123456789012345678901234567890",
        description: "Test app description 1",
        status: AppStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
        impressions: 1000,
        clicks: 50,
        revenue: 25.5,
        ctr: 0.05, // 5% CTR
      },
      {
        id: "app-2",
        name: "Test App 2",
        appId: "app_2345678901234567",
        appSecret: "secret_2345678901234567890123456789012345678901",
        description: null,
        status: AppStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date("2024-01-02"),
        updatedAt: new Date("2024-01-02"),
        impressions: 500,
        clicks: 20,
        revenue: 10.0,
        ctr: 0.04, // 4% CTR
      },
    ];

    it("should return user apps when authenticated", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AppService.getUserApps).mockResolvedValue(mockApps);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.apps).toEqual(stringifyDates(mockApps));
      expect(AppService.getUserApps).toHaveBeenCalledWith("user-123");
    });

    it("should return 401 when not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
      expect(AppService.getUserApps).not.toHaveBeenCalled();
    });

    it("should handle service errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AppService.getUserApps).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });
  });

  describe("POST /api/apps", () => {
    const mockCreatedApp = {
      id: "app-123",
      name: "New Test App",
      appId: "app_3456789012345678",
      appSecret: "secret_3456789012345678901234567890123456789012",
      description: "New test app description",
      status: AppStatus.ACTIVE,
      userId: "user-123",
      createdAt: new Date("2024-01-03"),
      updatedAt: new Date("2024-01-03"),
      impressions: 0,
      clicks: 0,
      revenue: 0,
      ctr: 0, // 0% CTR
    };

    it("should create an app with valid data", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      const requestBody = {
        name: "New Test App",
        description: "New test app description",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AppService.createApp).mockResolvedValue(mockCreatedApp);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message: "Application created successfully",
        app: stringifyDates(mockCreatedApp),
      });
      expect(AppService.createApp).toHaveBeenCalledWith(
        "user-123",
        "New Test App",
        "New test app description",
      );
    });

    it("should create an app without optional description", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      const requestBody = {
        name: "New Test App",
      };

      const mockCreatedAppNoDesc = { ...mockCreatedApp, description: null };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AppService.createApp).mockResolvedValue(mockCreatedAppNoDesc);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AppService.createApp).toHaveBeenCalledWith(
        "user-123",
        "New Test App",
        undefined,
      );
    });

    it("should return 401 when not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const requestBody = {
        name: "Test App",
        description: "Test description",
      };

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
      expect(AppService.createApp).not.toHaveBeenCalled();
    });

    // Validation tests
    it("should return 400 for missing app name", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        description: "Test description",
        // Missing name
      };

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("App name is required");
      expect(AppService.createApp).not.toHaveBeenCalled();
    });

    it("should return 400 for app name too short", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "AB", // Less than 3 characters
        description: "Test description",
      };

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain(
        "App name must be at least 3 characters long",
      );
      expect(AppService.createApp).not.toHaveBeenCalled();
    });

    it("should return 400 for app name too long", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "A".repeat(101), // More than 100 characters
        description: "Test description",
      };

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("App name too long");
      expect(AppService.createApp).not.toHaveBeenCalled();
    });

    it("should return 400 for description too long", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test App",
        description: "A".repeat(501), // More than 500 characters
      };

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Description too long");
      expect(AppService.createApp).not.toHaveBeenCalled();
    });

    // Service error handling tests
    it("should handle AuthenticationError from service", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      const requestBody = {
        name: "Test App",
        description: "Test description",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const { AuthenticationError } = await import("@/lib/errors");

      vi.mocked(AppService.createApp).mockRejectedValue(
        new AuthenticationError(
          "User must have MODEL_PROVIDER role to create apps",
        ),
      );

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe(
        "User must have MODEL_PROVIDER role to create apps",
      );
    });

    it("should handle generic service errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      const requestBody = {
        name: "Test App",
        description: "Test description",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AppService.createApp).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      const requestBody = {
        name: "Test App",
        description: "Test description",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Unique constraint failed on the fields: (`appId`)",
        {
          code: "P2002",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(AppService.createApp).mockRejectedValue(prismaError);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toContain("Unique constraint failed");
    });

    it("should return 400 for invalid JSON", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost:3000/api/apps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: "invalid json",
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AppService.createApp).not.toHaveBeenCalled();
    });
  });
});
