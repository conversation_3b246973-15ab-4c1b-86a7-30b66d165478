// app/api/analytics/[role]/route.ts

// 1️⃣ Prevent any static prerender/analysis—this runs at request time
export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { AnalyticsService } from "@/lib/services/analytics";
import { withAPIErrorHandling } from "@/lib/error-handler";

// ── Shape of your analytics SDK’s ads array (ctr is a string) ─────────

async function getAnalyticsHandler(
  request: NextRequest,
  { params }: { params: Promise<{ role: "model" | "advertiser" }> },
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const { role } = await params;

  if (!role) {
    return NextResponse.json(
      { error: "Role parameter is required" },
      { status: 400 },
    );
  }

  if (!role || (role !== "model" && role !== "advertiser")) {
    return NextResponse.json({ error: "Invalid role" }, { status: 400 });
  }

  const analytics = await AnalyticsService.getRoleAnalytics({
    userId: session.user.id,
    role,
    userRoles: session.user.roles,
  });

  return NextResponse.json(analytics);
}

export const GET = withAPIErrorHandling(getAnalyticsHandler);
