import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { BidType, Prisma, Role } from "@prisma/client";

import { GET } from "./route";

import { AnalyticsService } from "@/lib/services/analytics";
import { stringifyDates } from "@/lib/testUtils";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/services/analytics", () => ({
  AnalyticsService: {
    getRoleAnalytics: vi.fn(),
  },
}));

describe("/api/analytics/[role]", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/analytics/[role]", () => {
    const mockModelAnalytics = {
      totalApps: 5,
      totalImpressions: 10000,
      totalClicks: 500,
      totalRevenue: 250.75,
      monthlyData: [
        { month: "2024-01", impressions: 3000, clicks: 150, revenue: 75.25 },
        { month: "2024-02", impressions: 4000, clicks: 200, revenue: 100.5 },
        { month: "2024-03", impressions: 3000, clicks: 150, revenue: 75.0 },
      ],
      topApps: [
        {
          id: "app-1",
          name: "Top App 1",
          createdAt: new Date("2024-01-01"),
          impressions: 5000,
          clicks: 250,
          revenue: 125.25,
        },
        {
          id: "app-2",
          name: "Top App 2",
          createdAt: new Date("2024-01-15"),
          impressions: 3000,
          clicks: 150,
          revenue: 75.5,
        },
      ],
    };

    const mockAdvertiserAnalytics = {
      totalCampaigns: 3,
      totalBudget: 5000,
      totalSpend: 1250.5,
      totalImpressions: 8000,
      totalClicks: 400,
      averageCTR: 5,
      monthlyData: [
        { month: "2024-01", impressions: 2500, clicks: 125, spend: 312.5 },
        { month: "2024-02", impressions: 3000, clicks: 150, spend: 375.0 },
        { month: "2024-03", impressions: 2500, clicks: 125, spend: 563.0 },
      ],
      topCampaigns: [
        {
          id: "ad-1",
          name: "Top Campaign 1",
          budget: 2000,
          bidType: BidType.CPC,
          bidAmount: 0.5,
          createdAt: new Date("2024-01-01"),
          impressions: 4000,
          clicks: 200,
          spend: 625.25,
          ctr: 5.0,
        },
        {
          id: "ad-2",
          name: "Top Campaign 2",
          budget: 1500,
          bidType: BidType.CPM,
          bidAmount: 2.0,
          createdAt: new Date("2024-01-15"),
          impressions: 2500,
          clicks: 125,
          spend: 312.75,
          ctr: 5.0,
        },
      ],
    };

    it("should return model analytics for model role", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: [Role.MODEL_PROVIDER],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AnalyticsService.getRoleAnalytics).mockResolvedValue(
        mockModelAnalytics,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
          headers: {
            host: "localhost:3000",
          },
        },
      );

      const response = await GET(request, { params: { role: "model" } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(stringifyDates(mockModelAnalytics));
      expect(AnalyticsService.getRoleAnalytics).toHaveBeenCalledWith({
        userId: "user-123",
        role: "model",
        userRoles: ["MODEL_PROVIDER"],
      });
    });

    it("should return advertiser analytics for advertiser role", async () => {
      const mockSession = {
        user: {
          id: "user-456",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AnalyticsService.getRoleAnalytics).mockResolvedValue(
        mockAdvertiserAnalytics,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/advertiser",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "advertiser" },
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(stringifyDates(mockAdvertiserAnalytics));
      expect(AnalyticsService.getRoleAnalytics).toHaveBeenCalledWith({
        userId: "user-456",
        role: "advertiser",
        userRoles: ["ADVERTISER"],
      });
    });

    it("should handle user with multiple roles", async () => {
      const mockSession = {
        user: {
          id: "user-789",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER", "ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AnalyticsService.getRoleAnalytics).mockResolvedValue(
        mockModelAnalytics,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "model" },
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(stringifyDates(mockModelAnalytics));
      expect(AnalyticsService.getRoleAnalytics).toHaveBeenCalledWith({
        userId: "user-789",
        role: "model",
        userRoles: ["MODEL_PROVIDER", "ADVERTISER"],
      });
    });

    it("should return 401 when not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "model" },
      });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
      expect(AnalyticsService.getRoleAnalytics).not.toHaveBeenCalled();
    });

    it("should extract role parameter from URL path", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AnalyticsService.getRoleAnalytics).mockResolvedValue(
        mockAdvertiserAnalytics,
      );

      // Test with different URL structure
      const request = new NextRequest(
        "http://localhost:3000/api/analytics/advertiser",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "advertiser" },
      });

      expect(response.status).toBe(200);
      expect(AnalyticsService.getRoleAnalytics).toHaveBeenCalledWith({
        userId: "user-123",
        role: "advertiser",
        userRoles: ["ADVERTISER"],
      });
    });

    // Service error handling tests
    it("should handle InvalidRoleError from service", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const { InvalidRoleError } = await import("@/lib/errors");

      vi.mocked(AnalyticsService.getRoleAnalytics).mockRejectedValue(
        new InvalidRoleError("Invalid role. Must be 'model' or 'advertiser'"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/invalid",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "invalid" },
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid role");
    });

    it("should handle InsufficientPermissionsError from service", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const { InsufficientPermissionsError } = await import("@/lib/errors");

      vi.mocked(AnalyticsService.getRoleAnalytics).mockRejectedValue(
        new InsufficientPermissionsError(
          "User does not have permission to access advertiser analytics",
        ),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/advertiser",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "advertiser" },
      });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe(
        "User does not have permission to access advertiser analytics",
      );
    });

    it("should handle AnalyticsDataError from service", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const { AnalyticsDataError } = await import("@/lib/errors");

      vi.mocked(AnalyticsService.getRoleAnalytics).mockRejectedValue(
        new AnalyticsDataError("Failed to retrieve analytics data"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "model" },
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Failed to retrieve analytics data");
    });

    it("should handle generic service errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AnalyticsService.getRoleAnalytics).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "model" },
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["MODEL_PROVIDER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Record to delete does not exist.",
        {
          code: "P2025",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(AnalyticsService.getRoleAnalytics).mockRejectedValue(
        prismaError,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/analytics/model",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { role: "model" },
      });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain("Record to delete does not exist");
    });
  });
});
