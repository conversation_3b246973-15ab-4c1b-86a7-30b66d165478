import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import Strip<PERSON> from "stripe";

import { prisma } from "@/lib/db";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-05-28.basil",
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  const body = await request.text();
  const headersList = headers();
  const sig = headersList.get("stripe-signature");

  if (!sig) {
    console.error("Missing Stripe signature");

    return NextResponse.json(
      { error: "Missing Stripe signature" },
      { status: 400 },
    );
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
  } catch (err) {
    console.error("Webhook signature verification failed:", err);

    return NextResponse.json(
      { error: "Webhook signature verification failed" },
      { status: 400 },
    );
  }

  console.log(`Received Stripe webhook: ${event.type}`);

  try {
    switch (event.type) {
      case "account.updated":
        await handleAccountUpdated(event.data.object as Stripe.Account);
        break;

      case "payment_intent.succeeded":
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case "payment_intent.payment_failed":
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case "transfer.created":
        await handleTransferCreated(event.data.object as Stripe.Transfer);
        break;

      case "transfer.failed":
        await handleTransferFailed(event.data.object as Stripe.Transfer);
        break;

      case "account.application.deauthorized":
        await handleAccountDeauthorized(event.data.object as Stripe.Account);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Error processing webhook:", error);

    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 },
    );
  }
}

/**
 * Handle Stripe Connect account updates
 */
async function handleAccountUpdated(account: Stripe.Account) {
  console.log(`Account updated: ${account.id}`);

  // Find user with this Stripe Connect account
  const user = await prisma.user.findFirst({
    where: { stripeConnectAccountId: account.id },
  });

  if (!user) {
    console.log(`No user found for Stripe account: ${account.id}`);

    return;
  }

  // Log account status change for monitoring
  console.log(`Account ${account.id} status:`, {
    charges_enabled: account.charges_enabled,
    payouts_enabled: account.payouts_enabled,
    details_submitted: account.details_submitted,
  });

  // You could send notifications to users here about account status changes
  // For example, when their account becomes enabled for payouts
}

/**
 * Handle successful payment intents
 */
async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log(`Payment succeeded: ${paymentIntent.id}`);

  const userId = paymentIntent.metadata?.platform_user_id;

  if (!userId) {
    console.log("No platform user ID in payment metadata");

    return;
  }

  // Log successful payment for monitoring
  console.log(`Payment ${paymentIntent.id} succeeded for user ${userId}:`, {
    amount: paymentIntent.amount / 100,
    currency: paymentIntent.currency,
  });

  // You could update advertisement budgets here if needed
  // or send confirmation emails to users
}

/**
 * Handle failed payment intents
 */
async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log(`Payment failed: ${paymentIntent.id}`);

  const userId = paymentIntent.metadata?.platform_user_id;

  if (!userId) {
    console.log("No platform user ID in payment metadata");

    return;
  }

  // Log failed payment for monitoring
  console.log(`Payment ${paymentIntent.id} failed for user ${userId}:`, {
    amount: paymentIntent.amount / 100,
    currency: paymentIntent.currency,
    last_payment_error: paymentIntent.last_payment_error,
  });

  // You could send notification emails about failed payments here
}

/**
 * Handle transfer creation (payouts to connected accounts)
 */
async function handleTransferCreated(transfer: Stripe.Transfer) {
  console.log(`Transfer created: ${transfer.id}`);

  const userId = transfer.metadata?.platform_user_id;

  if (!userId) {
    console.log("No platform user ID in transfer metadata");

    return;
  }

  // Log successful transfer for monitoring
  console.log(`Transfer ${transfer.id} created for user ${userId}:`, {
    amount: transfer.amount / 100,
    currency: transfer.currency,
    destination: transfer.destination,
  });

  // You could send payout confirmation emails here
}

/**
 * Handle failed transfers
 */
async function handleTransferFailed(transfer: Stripe.Transfer) {
  console.log(`Transfer failed: ${transfer.id}`);

  const userId = transfer.metadata?.platform_user_id;

  if (!userId) {
    console.log("No platform user ID in transfer metadata");

    return;
  }

  // Log failed transfer for monitoring
  console.log(`Transfer ${transfer.id} failed for user ${userId}:`, {
    amount: transfer.amount / 100,
    currency: transfer.currency,
    destination: transfer.destination,
    failure_code: transfer.failure_code,
    failure_message: transfer.failure_message,
  });

  // You could send notification emails about failed payouts here
}

/**
 * Handle account deauthorization (user disconnects their account)
 */
async function handleAccountDeauthorized(account: Stripe.Account) {
  console.log(`Account deauthorized: ${account.id}`);

  // Find and update user to remove Stripe Connect account ID
  const user = await prisma.user.findFirst({
    where: { stripeConnectAccountId: account.id },
  });

  if (user) {
    await prisma.user.update({
      where: { id: user.id },
      data: { stripeConnectAccountId: null },
    });

    console.log(`Removed Stripe Connect account from user ${user.id}`);
  }
}
