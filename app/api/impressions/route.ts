import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { AdService } from "@/lib/services/ad";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Enhanced impression tracking validation
const impressionTrackingSchema = z.object({
  adId: z.string({ error: "Ad ID is required." }),
  appId: z.string({ error: "App ID is required." }),
  clicked: z.boolean().default(false),
  userAgent: z.string().optional(),
});

async function recordImpressionHandler(
  request: NextRequest,
  validatedData: z.infer<typeof impressionTrackingSchema>,
): Promise<NextResponse> {
  const { adId, appId, clicked, userAgent } = validatedData;

  // Get client IP address
  const forwarded = request.headers.get("x-forwarded-for");
  const ipAddress = forwarded
    ? forwarded.split(",")[0]
    : request.headers.get("x-real-ip") || "unknown";

  // Use AdService to record impression
  await AdService.recordImpression(adId, appId, clicked, userAgent, ipAddress);

  return NextResponse.json(
    {
      message: "Impression recorded successfully",
      clicked,
    },
    { status: 201 },
  );
}

// Apply error handling middleware
export const POST = withValidationAPIErrorHandling(
  recordImpressionHandler,
  impressionTrackingSchema,
);
