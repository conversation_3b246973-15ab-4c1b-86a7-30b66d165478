import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Prisma } from "@prisma/client";

import { POST } from "./route";

import { AdService } from "@/lib/services/ad";

// Mock the AdService
vi.mock("@/lib/services/ad", () => ({
  AdService: {
    recordImpression: vi.fn(),
  },
}));

describe("/api/impressions", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/impressions", () => {
    it("should record an impression with valid data", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
        userAgent:
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-forwarded-for": "***********, ********",
        },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message: "Impression recorded successfully",
        clicked: false,
      });
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "***********",
      );
    });

    it("should record a click impression", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: true,
        userAgent:
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-real-ip": "***********",
        },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message: "Impression recorded successfully",
        clicked: true,
      });
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        true,
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "***********",
      );
    });

    it("should record impression with minimal required fields", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message: "Impression recorded successfully",
        clicked: false, // Default value
      });
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        undefined,
        "unknown", // Default IP when no headers present
      );
    });

    it("should extract IP from x-forwarded-for header (first IP)", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-forwarded-for": "***********, ***********, ********",
        },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        undefined,
        "***********", // First IP from x-forwarded-for
      );
    });

    it("should fallback to x-real-ip when x-forwarded-for is not present", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-real-ip": "************",
        },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        undefined,
        "************",
      );
    });

    it("should use 'unknown' IP when no IP headers are present", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        undefined,
        "unknown",
      );
    });

    // Validation tests
    it("should return 400 for missing adId", async () => {
      const requestBody = {
        appId: "app-456",
        clicked: false,
      };

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Ad ID is required");
      expect(AdService.recordImpression).not.toHaveBeenCalled();
    });

    it("should return 400 for missing appId", async () => {
      const requestBody = {
        adId: "ad-123",
        clicked: false,
      };

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("App ID is required");
      expect(AdService.recordImpression).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: "invalid json",
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AdService.recordImpression).not.toHaveBeenCalled();
    });

    it("should handle clicked as boolean true", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: true,
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.clicked).toBe(true);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        true,
        undefined,
        "unknown",
      );
    });

    // Service error handling tests
    it("should handle AdNotFoundError from service", async () => {
      const requestBody = {
        adId: "non-existent-ad",
        appId: "app-456",
        clicked: false,
      };

      const { AdNotFoundError } = await import("@/lib/errors");

      vi.mocked(AdService.recordImpression).mockRejectedValue(
        new AdNotFoundError("Advertisement not found"),
      );

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("Advertisement not found");
    });

    it("should handle InvalidAppCredentialsError from service", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "invalid-app",
        clicked: false,
      };

      const { InvalidAppCredentialsError } = await import("@/lib/errors");

      vi.mocked(AdService.recordImpression).mockRejectedValue(
        new InvalidAppCredentialsError("Invalid app credentials"),
      );

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Invalid app credentials");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
      };

      vi.mocked(AdService.recordImpression).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
      };

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Foreign key constraint violation",
        {
          code: "P2003",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(AdService.recordImpression).mockRejectedValue(prismaError);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Foreign key constraint violation");
    });

    it("should handle userAgent as optional string", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
        userAgent: "Custom User Agent String",
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        "Custom User Agent String",
        "unknown",
      );
    });

    it("should handle missing userAgent gracefully", async () => {
      const requestBody = {
        adId: "ad-123",
        appId: "app-456",
        clicked: false,
        // userAgent is optional and not provided
      };

      vi.mocked(AdService.recordImpression).mockResolvedValue(undefined);

      const request = new NextRequest("http://localhost:3000/api/impressions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.recordImpression).toHaveBeenCalledWith(
        "ad-123",
        "app-456",
        false,
        undefined,
        "unknown",
      );
    });
  });
});
