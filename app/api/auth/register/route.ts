import { NextRequest, NextResponse } from "next/server";
import { Role } from "@prisma/client";
import { z } from "zod/v4";

import { AuthService } from "@/lib/services/auth";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// User registration validation
const registerSchema = z.object({
  email: z.email("Invalid email format"),
  password: z
    .string({ error: "Password is required" })
    .min(8, "Password must be at least 8 characters long"),
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .optional()
    .default([]), // Roles are optional during registration, default to empty array
});

async function registerHandler(
  request: NextRequest,
  validatedData: z.infer<typeof registerSchema>,
): Promise<NextResponse> {
  const { email, password, roles } = validatedData;

  // Use AuthService to register user
  const user = await AuthService.registerUser({
    email,
    password,
    roles: roles as Role[],
  });

  return NextResponse.json(
    {
      message:
        "User registered successfully. Please check your email for verification.",
      user,
      emailSent: true,
    },
    { status: 201 },
  );
}

// Apply error handling only - middleware handles security and rate limiting
export const POST = withValidationAPIErrorHandling(
  registerHandler,
  registerSchema,
);
