import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Role } from "@prisma/client";

import { POST } from "./route";

import { AuthService } from "@/lib/services/auth";
import { stringifyDates } from "@/lib/testUtils";

// Mock the AuthService
vi.mock("@/lib/services/auth", () => ({
  AuthService: {
    registerUser: vi.fn(),
  },
}));

describe("/api/auth/register", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/register", () => {
    const mockRegisteredUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: null,
      image: null,
      roles: [Role.MODEL_PROVIDER],
      emailVerified: null,
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date("2024-01-01"),
    };

    it("should register a user with valid data", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(AuthService.registerUser).mockResolvedValue(mockRegisteredUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message:
          "User registered successfully. Please check your email for verification.",
        user: stringifyDates(mockRegisteredUser),
        emailSent: true,
      });
      expect(AuthService.registerUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      });
    });

    it("should register a user with multiple roles", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER", "ADVERTISER"],
      };

      const mockMultiRoleUser = {
        ...mockRegisteredUser,
        email: "<EMAIL>",
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      };

      vi.mocked(AuthService.registerUser).mockResolvedValue(mockMultiRoleUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.user.roles).toEqual(["MODEL_PROVIDER", "ADVERTISER"]);
      expect(AuthService.registerUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER", "ADVERTISER"],
      });
    });

    it("should register a user with ADVERTISER role", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["ADVERTISER"],
      };

      const mockAdvertiserUser = {
        ...mockRegisteredUser,
        email: "<EMAIL>",
        roles: [Role.ADVERTISER],
      };

      vi.mocked(AuthService.registerUser).mockResolvedValue(mockAdvertiserUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.user.roles).toEqual(["ADVERTISER"]);
      expect(AuthService.registerUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["ADVERTISER"],
      });
    });

    it("should register a user with empty roles (default)", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        // roles not provided, should default to empty array
      };

      const mockNoRolesUser = {
        ...mockRegisteredUser,
        email: "<EMAIL>",
        roles: [],
      };

      vi.mocked(AuthService.registerUser).mockResolvedValue(mockNoRolesUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.user.roles).toEqual([]);
      expect(AuthService.registerUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "securepassword123",
        roles: [],
      });
    });

    // Validation tests
    it("should return 400 for invalid email format", async () => {
      const requestBody = {
        email: "invalid-email",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    it("should return 400 for missing email", async () => {
      const requestBody = {
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    it("should return 400 for missing password", async () => {
      const requestBody = {
        email: "<EMAIL>",
        roles: ["MODEL_PROVIDER"],
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("required");
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    it("should return 400 for password too short", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "short",
        roles: ["MODEL_PROVIDER"],
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain(
        "Password must be at least 8 characters long",
      );
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid role", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["INVALID_ROLE"],
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid option");
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AuthService.registerUser).not.toHaveBeenCalled();
    });

    // Service error handling tests
    it("should handle AuthenticationError from service (user already exists)", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      const { AuthenticationError } = await import("@/lib/errors");

      vi.mocked(AuthService.registerUser).mockRejectedValue(
        new AuthenticationError(
          "User with this email already exists, please sign in.",
        ),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe(
        "User with this email already exists, please sign in.",
      );
    });

    it("should handle InvalidRolesError from service", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: [Role.MODEL_PROVIDER, Role.ADVERTISER, Role.ADVERTISER],
      };

      const { InvalidRolesError } = await import("@/lib/errors");

      vi.mocked(AuthService.registerUser).mockRejectedValue(
        new InvalidRolesError("Maximum two roles allowed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Maximum two roles allowed");
    });

    it("should handle EmailSendError from service", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      const { EmailSendError } = await import("@/lib/errors");

      vi.mocked(AuthService.registerUser).mockRejectedValue(
        new EmailSendError("Failed to send verification email"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Failed to send verification email");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        email: "<EMAIL>",
        password: "securepassword123",
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(AuthService.registerUser).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/register",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });
  });
});
