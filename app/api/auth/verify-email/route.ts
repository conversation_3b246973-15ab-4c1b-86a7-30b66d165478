import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { AuthService } from "@/lib/services/auth";
import {
  withAPIErrorHandling,
  withValidationAPIErrorHandling,
} from "@/lib/error-handler";

// Email verification validation
const emailVerificationSchema = z.object({
  token: z.string({ error: "Verification token is required" }),
});

async function verifyEmailHandler(
  request: NextRequest,
  validatedData: z.infer<typeof emailVerificationSchema>,
): Promise<NextResponse> {
  const { token } = validatedData;

  // Use AuthService to verify email
  await AuthService.verifyUserEmail(token);

  return NextResponse.json(
    {
      message: "Email verified successfully",
    },
    { status: 200 },
  );
}

// Apply error handling middleware - middleware handles security and rate limiting
export const POST = withValidationAPIErrorHandling(
  verifyEmailHandler,
  emailVerificationSchema,
);

async function getVerifyEmailHandler(
  request: NextRequest,
  { params }: { params: { token: string } },
): Promise<NextResponse> {
  const { token } = params;

  if (!token) {
    return NextResponse.json(
      { error: "Verification token is required" },
      { status: 400 },
    );
  }

  await AuthService.verifyUserEmail(token);

  return NextResponse.json(
    {
      message: "Email verified successfully",
    },
    { status: 200 },
  );
}

export const GET = withAPIErrorHandling(getVerifyEmailHandler);
