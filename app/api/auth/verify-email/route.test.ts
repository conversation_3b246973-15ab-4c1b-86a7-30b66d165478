import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Prisma } from "@prisma/client";

import { GET, POST } from "./route";

import { AuthService } from "@/lib/services/auth";

// Mock the AuthService
vi.mock("@/lib/services/auth", () => ({
  AuthService: {
    verifyUserEmail: vi.fn(),
  },
}));

describe("/api/auth/verify-email", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/verify-email", () => {
    it("should verify email with valid token", async () => {
      const requestBody = {
        token: "valid-verification-token-123",
      };

      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue(undefined);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: "Email verified successfully",
      });
      expect(AuthService.verifyUserEmail).toHaveBeenCalledWith(
        "valid-verification-token-123",
      );
    });

    it("should return 400 for missing token", async () => {
      const requestBody = {
        // Missing token
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Verification token is required");
      expect(AuthService.verifyUserEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for empty token", async () => {
      const requestBody = {};

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Verification token is required");
      expect(AuthService.verifyUserEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AuthService.verifyUserEmail).not.toHaveBeenCalled();
    });

    // Service error handling tests for POST
    it("should handle InvalidVerificationTokenError from service", async () => {
      const requestBody = {
        token: "invalid-token",
      };

      const { InvalidVerificationTokenError } = await import("@/lib/errors");

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new InvalidVerificationTokenError(
          "Invalid or expired verification token",
        ),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should handle EmailAlreadyVerifiedError from service", async () => {
      const requestBody = {
        token: "already-verified-token",
      };

      const { EmailAlreadyVerifiedError } = await import("@/lib/errors");

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new EmailAlreadyVerifiedError("Email is already verified"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toBe("Email is already verified");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        token: "valid-token",
      };

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });
  });

  describe("GET /api/auth/verify-email", () => {
    it("should verify email with token in query parameter", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue(undefined);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=valid-token-123",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "valid-token-123" },
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: "Email verified successfully",
      });
      expect(AuthService.verifyUserEmail).toHaveBeenCalledWith(
        "valid-token-123",
      );
    });

    it("should return 400 when token query parameter is missing", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: {},
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Verification token is required");
      expect(AuthService.verifyUserEmail).not.toHaveBeenCalled();
    });

    it("should handle URL with multiple query parameters", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockResolvedValue(undefined);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=valid-token-456&redirect=dashboard",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "valid-token-456", redirect: "dashboard" },
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: "Email verified successfully",
      });
      expect(AuthService.verifyUserEmail).toHaveBeenCalledWith(
        "valid-token-456",
      );
    });

    // Service error handling tests for GET
    it("should handle InvalidVerificationTokenError from service (GET)", async () => {
      const { InvalidVerificationTokenError } = await import("@/lib/errors");

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new InvalidVerificationTokenError(
          "Invalid or expired verification token",
        ),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=invalid-token",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "invalid-token" },
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Invalid or expired verification token");
    });

    it("should handle EmailAlreadyVerifiedError from service (GET)", async () => {
      const { EmailAlreadyVerifiedError } = await import("@/lib/errors");

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new EmailAlreadyVerifiedError("Email is already verified"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=already-verified",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "already-verified" },
      });
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toBe("Email is already verified");
    });

    it("should handle UserNotFoundError from service (GET)", async () => {
      const { UserNotFoundError } = await import("@/lib/errors");

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new UserNotFoundError("User not found"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=orphaned-token",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "orphaned-token" },
      });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("User not found");
    });

    it("should handle generic service errors (GET)", async () => {
      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=valid-token",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "valid-token" },
      });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors (GET)", async () => {
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Record to delete does not exist.",
        {
          code: "P2025",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(AuthService.verifyUserEmail).mockRejectedValue(prismaError);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/verify-email?token=valid-token",
        {
          method: "GET",
        },
      );

      const response = await GET(request, {
        params: { token: "valid-token" },
      });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain("Record to delete does not exist");
    });
  });
});
