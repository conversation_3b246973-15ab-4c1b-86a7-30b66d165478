import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { AuthService } from "@/lib/services/auth";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Resend verification email validation
const resendVerificationSchema = z.object({
  email: z.email("Invalid email format"),
});

async function resendVerificationHandler(
  request: NextRequest,
  validatedData: z.infer<typeof resendVerificationSchema>,
): Promise<NextResponse> {
  const { email } = validatedData;

  // Use AuthService to resend verification email
  await AuthService.resendVerificationEmail(email);

  return NextResponse.json(
    {
      message:
        "If an account with this email exists and is not verified, a verification email has been sent.",
    },
    { status: 200 },
  );
}

// Apply error handling middleware - middleware handles security and rate limiting
export const POST = withValidationAPIErrorHandling(
  resend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  resendVerificationSchema,
);
