import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Prisma } from "@prisma/client";

import { POST } from "./route";

import { AuthService } from "@/lib/services/auth";

// Mock the AuthService
vi.mock("@/lib/services/auth", () => ({
  AuthService: {
    resendVerificationEmail: vi.fn(),
  },
}));

describe("/api/auth/resend-verification", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/resend-verification", () => {
    it("should resend verification email with valid email", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      vi.mocked(AuthService.resendVerificationEmail).mockResolvedValue(
        undefined,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message:
          "If an account with this email exists and is not verified, a verification email has been sent.",
      });
      expect(AuthService.resendVerificationEmail).toHaveBeenCalledWith(
        "<EMAIL>",
      );
    });

    it("should handle different email formats", async () => {
      const testEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      for (const email of testEmails) {
        vi.clearAllMocks();
        vi.mocked(AuthService.resendVerificationEmail).mockResolvedValue(
          undefined,
        );

        const requestBody = { email };

        const request = new NextRequest(
          "http://localhost:3000/api/auth/resend-verification",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestBody),
          },
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.message).toContain("verification email has been sent");
        expect(AuthService.resendVerificationEmail).toHaveBeenCalledWith(email);
      }
    });

    it("should return consistent message for non-existent email (security)", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      // Service should handle this gracefully without throwing
      vi.mocked(AuthService.resendVerificationEmail).mockResolvedValue(
        undefined,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "If an account with this email exists and is not verified, a verification email has been sent.",
      );
      expect(AuthService.resendVerificationEmail).toHaveBeenCalledWith(
        "<EMAIL>",
      );
    });

    it("should return consistent message for already verified email (security)", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      // Service should handle this gracefully without throwing
      vi.mocked(AuthService.resendVerificationEmail).mockResolvedValue(
        undefined,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe(
        "If an account with this email exists and is not verified, a verification email has been sent.",
      );
      expect(AuthService.resendVerificationEmail).toHaveBeenCalledWith(
        "<EMAIL>",
      );
    });

    // Validation tests
    it("should return 400 for invalid email format", async () => {
      const requestBody = {
        email: "invalid-email-format",
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for missing email", async () => {
      const requestBody = {
        // Missing email field
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for empty email", async () => {
      const requestBody = {
        email: "",
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for null email", async () => {
      const requestBody = {
        email: null,
      };

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid email format");
      expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
    });

    it("should handle various invalid email formats", async () => {
      const invalidEmails = [
        "plainaddress",
        "@missingdomain.com",
        "missing@.com",
        "missing@domain",
        "spaces @domain.com",
        "user@",
        "user@@domain.com",
        "<EMAIL>",
      ];

      for (const email of invalidEmails) {
        vi.clearAllMocks();

        const requestBody = { email };

        const request = new NextRequest(
          "http://localhost:3000/api/auth/resend-verification",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestBody),
          },
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toContain("Invalid email format");
        expect(AuthService.resendVerificationEmail).not.toHaveBeenCalled();
      }
    });

    // Service error handling tests
    it("should handle EmailSendError from service", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      const { EmailSendError } = await import("@/lib/errors");

      vi.mocked(AuthService.resendVerificationEmail).mockRejectedValue(
        new EmailSendError("Failed to send verification email"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Failed to send verification email");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      vi.mocked(AuthService.resendVerificationEmail).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Unique constraint failed on the fields: (`email`)",
        {
          clientVersion: "4.0.0",
          code: "P2002",
        },
      );

      vi.mocked(AuthService.resendVerificationEmail).mockRejectedValue(
        prismaError,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toContain("Unique constraint failed");
    });

    it("should handle email with special characters", async () => {
      const requestBody = {
        email: "<EMAIL>",
      };

      vi.mocked(AuthService.resendVerificationEmail).mockResolvedValue(
        undefined,
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/resend-verification",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toContain("verification email has been sent");
      expect(AuthService.resendVerificationEmail).toHaveBeenCalledWith(
        "<EMAIL>",
      );
    });
  });
});
