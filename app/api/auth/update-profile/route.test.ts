import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { Prisma, Role } from "@prisma/client";

import { POST } from "./route";

import { UserService } from "@/lib/services/user";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/services/user", () => ({
  UserService: {
    updateUser: vi.fn(),
  },
}));

vi.mock("@/lib/auth", () => ({
  authOptions: {},
}));

describe("/api/auth/update-profile", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/update-profile", () => {
    const mockUpdatedUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Updated Name",
      image: null,
      roles: [Role.MODEL_PROVIDER],
      emailVerified: new Date("2024-01-01"),
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date("2024-01-01"),
    };

    const mockSession = {
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: [Role.MODEL_PROVIDER],
      },
    };

    it("should update user name successfully", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(UserService.updateUser).mockResolvedValue(mockUpdatedUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Profile updated successfully");
      expect(data.user).toEqual({
        id: mockUpdatedUser.id,
        email: mockUpdatedUser.email,
        name: mockUpdatedUser.name,
        roles: mockUpdatedUser.roles,
        emailVerified: mockUpdatedUser.emailVerified.toISOString(),
        createdAt: mockUpdatedUser.createdAt.toISOString(),
      });
      expect(UserService.updateUser).toHaveBeenCalledWith("user-123", {
        name: "Updated Name",
      });
    });

    it("should handle empty request body", async () => {
      const requestBody = {};

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(UserService.updateUser).mockResolvedValue(mockUpdatedUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Profile updated successfully");
      expect(UserService.updateUser).toHaveBeenCalledWith("user-123", {});
    });

    it("should return 401 when no session", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Authentication required");
      expect(UserService.updateUser).not.toHaveBeenCalled();
    });

    it("should return 401 when session has no user id", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      vi.mocked(getServerSession).mockResolvedValue({
        user: {},
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Authentication required");
      expect(UserService.updateUser).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid name (too long)", async () => {
      const requestBody = {
        name: "a".repeat(101), // Too long
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Name must be at most 100 characters long");
      expect(UserService.updateUser).not.toHaveBeenCalled();
    });

    it("should return 400 for empty name string", async () => {
      const requestBody = {
        name: "",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Name must be at least 1 character long");
      expect(UserService.updateUser).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(UserService.updateUser).not.toHaveBeenCalled();
    });

    // Service error handling tests
    it("should handle UserNotFoundError from service", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      const { UserNotFoundError } = await import("@/lib/errors");

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(UserService.updateUser).mockRejectedValue(
        new UserNotFoundError("User not found"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("User not found");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(UserService.updateUser).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const requestBody = {
        name: "Updated Name",
      };

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Record to update does not exist",
        {
          code: "P2025",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(UserService.updateUser).mockRejectedValue(prismaError);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/update-profile",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain("Record to update does not exist");
    });
  });
});
