import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { UserService } from "@/lib/services/user";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Validation schema
const updateProfileSchema = z.object({
  name: z
    .string({ error: "Name is required" })
    .min(1, "Name must be at least 1 character long")
    .max(100, "Name must be at most 100 characters long")
    .optional(),
});

async function updateProfileHandler(
  request: NextRequest,
  validatedData: z.infer<typeof updateProfileSchema>,
): Promise<NextResponse> {
  // Middleware handles authentication, but we still need session for user ID
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );
  }

  const { name } = validatedData;

  // Update user profile using UserService.updateUser - now returns data directly
  const updatedUser = await UserService.updateUser(session.user.id, {
    name,
  });

  return NextResponse.json(
    {
      message: "Profile updated successfully",
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        roles: updatedUser.roles,
        emailVerified: updatedUser.emailVerified,
        createdAt: updatedUser.createdAt,
      },
    },
    { status: 200 },
  );
}

// Apply error handling middleware - authentication handled by middleware
export const POST = withValidationAPIErrorHandling(
  updateProfileHandler,
  updateProfileSchema,
);
