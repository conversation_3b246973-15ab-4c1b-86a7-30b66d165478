import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { Role } from "@prisma/client";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { AuthService } from "@/lib/services/auth";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Role assignment validation (enhanced)
const assignRolesSchema = z.object({
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .min(1, "At least one role is required")
    .max(2, "Maximum two roles allowed")
    .refine(
      (roles) => new Set(roles).size === roles.length,
      "Duplicate roles are not allowed",
    ),
});

async function assignRolesHandler(
  request: NextRequest,
  validatedData: z.infer<typeof assignRolesSchema>,
): Promise<NextResponse> {
  // Middleware handles authentication, but we still need session for user ID
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );
  }

  const { roles } = validatedData;

  // Use AuthService to assign roles
  const user = await AuthService.assignUserRoles(
    session.user.id,
    roles as Role[],
  );

  return NextResponse.json(
    {
      message: "Roles assigned successfully",
      user,
    },
    { status: 200 },
  );
}

// Apply error handling middleware - authentication handled by middleware
export const POST = withValidationAPIErrorHandling(
  assignRolesHandler,
  assignRolesSchema,
);
