import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { Prisma, Role } from "@prisma/client";

import { POST } from "./route";

import { AuthService } from "@/lib/services/auth";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/services/auth", () => ({
  AuthService: {
    assignUserRoles: vi.fn(),
  },
}));

vi.mock("@/lib/auth", () => ({
  authOptions: {},
}));

describe("/api/auth/assign-roles", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/auth/assign-roles", () => {
    const mockUser = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      emailVerified: new Date("2024-01-01"),
      createdAt: new Date("2024-01-01"),
      updatedAt: new Date("2024-01-01"),
    };

    // Expected response with serialized dates
    const expectedUserResponse = {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      image: null,
      roles: [Role.MODEL_PROVIDER, Role.ADVERTISER],
      emailVerified: "2024-01-01T00:00:00.000Z",
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
    };

    const mockSession = {
      user: {
        id: "user-123",
        email: "<EMAIL>",
        roles: [],
      },
    };

    it("should assign single role successfully", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockResolvedValue(mockUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: "Roles assigned successfully",
        user: expectedUserResponse,
      });
      expect(AuthService.assignUserRoles).toHaveBeenCalledWith("user-123", [
        "MODEL_PROVIDER",
      ]);
    });

    it("should assign multiple roles successfully", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER", "ADVERTISER"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockResolvedValue(mockUser);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: "Roles assigned successfully",
        user: expectedUserResponse,
      });
      expect(AuthService.assignUserRoles).toHaveBeenCalledWith("user-123", [
        "MODEL_PROVIDER",
        "ADVERTISER",
      ]);
    });

    it("should return 401 when no session", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Authentication required");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 401 when session has no user id", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(getServerSession).mockResolvedValue({
        user: {},
      });

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Authentication required");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for empty roles array", async () => {
      const requestBody = {
        roles: [],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("At least one role is required");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for missing roles field", async () => {
      const requestBody = {};

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain(
        "Invalid input: expected array, received undefined",
      );
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid role", async () => {
      const requestBody = {
        roles: ["INVALID_ROLE"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid option: expected one of");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for too many roles", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER", "ADVERTISER", "ADMIN"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid option: expected one of");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for duplicate roles", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER", "MODEL_PROVIDER"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Duplicate roles are not allowed");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: "invalid json",
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });

    // Service error handling tests
    it("should handle UserNotFoundError from service", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      const { UserNotFoundError } = await import("@/lib/errors");

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockRejectedValue(
        new UserNotFoundError("User not found"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("User not found");
    });

    it("should handle InvalidRoleError from service", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      const { InvalidRoleError } = await import("@/lib/errors");

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockRejectedValue(
        new InvalidRoleError("Invalid role assignment"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe("Invalid role assignment");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const requestBody = {
        roles: ["MODEL_PROVIDER"],
      };

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Record to update does not exist",
        {
          clientVersion: "4.0.0",
          code: "P2025",
          meta: { model: "User", field: "id" },
        },
      );

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AuthService.assignUserRoles).mockRejectedValue(prismaError);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain("Record to update does not exist");
    });

    it("should handle roles as string instead of array", async () => {
      const requestBody = {
        roles: "MODEL_PROVIDER",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest(
        "http://localhost:3000/api/auth/assign-roles",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestBody),
        },
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain(
        "Invalid input: expected array, received string",
      );
      expect(AuthService.assignUserRoles).not.toHaveBeenCalled();
    });
  });
});
