import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import { withAPIErrorHandling } from "@/lib/error-handler";

async function getTransactions(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get user's transaction history
  const transactions = await PaymentService.getUserTransactions(
    session.user.id,
  );

  return NextResponse.json({ transactions });
}

export const GET = withAPIErrorHandling(getTransactions);
