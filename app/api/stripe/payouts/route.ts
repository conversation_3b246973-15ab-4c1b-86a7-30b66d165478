import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import {
  withValidationAPIErrorHandling,
  withAPIErrorHandling,
} from "@/lib/error-handler";

// Create payout validation
const createPayoutSchema = z.object({
  amount: z
    .number()
    .min(1, "Amount must be at least $1")
    .max(100000, "Amount too high"),
  currency: z.string().length(3).optional(),
  description: z.string().max(500, "Description too long").optional(),
});

async function getPayouts(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get user's payout history
  const payouts = await PaymentService.getUserPayouts(session.user.id);

  return NextResponse.json({ payouts });
}

async function createPayoutHandler(
  request: NextRequest,
  validatedData: z.infer<typeof createPayoutSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { amount, currency, description } = validatedData;

  // Create payout
  const payout = await PaymentService.createPayout({
    userId: session.user.id,
    amount,
    currency,
    description,
  });

  return NextResponse.json(
    {
      message: "Payout created successfully",
      transferId: payout.transferId,
      amount: payout.amount,
      currency: payout.currency,
      status: payout.status,
    },
    { status: 201 },
  );
}

export const GET = withAPIErrorHandling(getPayouts);
export const POST = withValidationAPIErrorHandling(
  createPayoutHandler,
  createPayoutSchema,
);
