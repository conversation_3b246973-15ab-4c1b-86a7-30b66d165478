import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import {
  withValidationAPIErrorHandling,
  withAPIErrorHandling,
} from "@/lib/error-handler";

// Create payment validation
const createPaymentSchema = z.object({
  amount: z
    .number()
    .min(1, "Amount must be at least $1")
    .max(100000, "Amount too high"),
  currency: z.string().length(3).optional(),
  description: z.string().max(500, "Description too long").optional(),
  metadata: z.record(z.string()).optional(),
});

async function getPayments(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get user's payment history
  const payments = await PaymentService.getUserPayments(session.user.id);

  return NextResponse.json({ payments });
}

async function createPaymentHandler(
  request: NextRequest,
  validatedData: z.infer<typeof createPaymentSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { amount, currency, description, metadata } = validatedData;

  // Create payment intent
  const result = await PaymentService.createPayment({
    userId: session.user.id,
    amount,
    currency,
    description,
    metadata,
  });

  return NextResponse.json(
    {
      message: "Payment created successfully",
      paymentIntentId: result.paymentIntentId,
      clientSecret: result.clientSecret,
      amount: result.amount,
      currency: result.currency,
      status: result.status,
    },
    { status: 201 },
  );
}

export const GET = withAPIErrorHandling(getPayments);
export const POST = withValidationAPIErrorHandling(
  createPaymentHandler,
  createPaymentSchema,
);
