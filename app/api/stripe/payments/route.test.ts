import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";

import { GET, POST } from "./route";

import { PaymentService } from "@/lib/services/payment";

// Mock next-auth
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

// Mock PaymentService
vi.mock("@/lib/services/payment", () => ({
  PaymentService: {
    getUserPayments: vi.fn(),
    createPayment: vi.fn(),
  },
}));

describe("/api/stripe/payments", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET", () => {
    it("should return 401 if user is not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/stripe/payments");
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();

      expect(data.error).toBe("Unauthorized");
    });

    it("should return user payments successfully", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      const mockPayments = [
        {
          id: "pi_123",
          amount: 100,
          currency: "usd",
          status: "succeeded",
          description: "Test payment",
          createdAt: "2022-01-01T00:00:00.000Z",
        },
      ];

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.getUserPayments).mockResolvedValue(mockPayments);

      const request = new NextRequest("http://localhost/api/stripe/payments");
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();

      expect(data.payments).toEqual(mockPayments);

      expect(PaymentService.getUserPayments).toHaveBeenCalledWith("user-123");
    });
  });

  describe("POST", () => {
    it("should return 401 if user is not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/stripe/payments", {
        method: "POST",
        body: JSON.stringify({ amount: 100 }),
      });
      const response = await POST(request);

      expect(response.status).toBe(401);
    });

    it("should create payment successfully", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      const mockPaymentResult = {
        paymentIntentId: "pi_123",
        clientSecret: "pi_123_secret",
        amount: 100,
        currency: "usd",
        status: "requires_payment_method",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.createPayment).mockResolvedValue(
        mockPaymentResult,
      );

      const request = new NextRequest("http://localhost/api/stripe/payments", {
        method: "POST",
        body: JSON.stringify({
          amount: 100,
          description: "Test payment",
          metadata: { test: "value" },
        }),
      });
      const response = await POST(request);

      expect(response.status).toBe(201);
      const data = await response.json();

      expect(data.message).toBe("Payment created successfully");
      expect(data.paymentIntentId).toBe("pi_123");
      expect(data.clientSecret).toBe("pi_123_secret");

      expect(PaymentService.createPayment).toHaveBeenCalledWith({
        userId: "user-123",
        amount: 100,
        currency: undefined,
        description: "Test payment",
        metadata: { test: "value" },
      });
    });

    it("should handle validation errors", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost/api/stripe/payments", {
        method: "POST",
        body: JSON.stringify({ amount: 0 }), // Invalid amount
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it("should handle amount too high", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost/api/stripe/payments", {
        method: "POST",
        body: JSON.stringify({ amount: 200000 }), // Too high
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it("should handle PaymentService errors", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.createPayment).mockRejectedValue(
        new Error("Payment creation failed"),
      );

      const request = new NextRequest("http://localhost/api/stripe/payments", {
        method: "POST",
        body: JSON.stringify({ amount: 100 }),
      });
      const response = await POST(request);

      expect(response.status).toBe(500);
    });
  });
});
