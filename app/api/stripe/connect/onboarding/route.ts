import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Create onboarding link validation
const onboardingLinkSchema = z.object({
  returnUrl: z.url("Invalid return URL"),
  refreshUrl: z.url("Invalid refresh URL"),
});

async function createOnboardingLinkHandler(
  request: NextRequest,
  validatedData: z.infer<typeof onboardingLinkSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { returnUrl, refreshUrl } = validatedData;

  // Create onboarding link
  const onboardingUrl = await PaymentService.createOnboardingLink(
    session.user.id,
    returnUrl,
    refreshUrl,
  );

  return NextResponse.json({
    onboardingUrl,
  });
}

export const POST = withValidationAPIErrorHandling(
  createOnboardingLinkHandler,
  onboardingLinkSchema,
);
