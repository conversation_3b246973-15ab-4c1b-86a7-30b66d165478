import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import { withAPIErrorHandling } from "@/lib/error-handler";

async function createDashboardLinkHandler(
  _request: NextRequest,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Create Express dashboard link
  const dashboardUrl = await PaymentService.createDashboardLink(
    session.user.id,
  );

  return NextResponse.json({
    dashboardUrl,
  });
}

export const POST = withAPIErrorHandling(createDashboardLinkHandler);
