import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";

import { GET, POST } from "./route";

import { PaymentService } from "@/lib/services/payment";

// Mock next-auth
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

// Mock PaymentService
vi.mock("@/lib/services/payment", () => ({
  PaymentService: {
    getStripeAccountStatus: vi.fn(),
    createStripeAccount: vi.fn(),
  },
}));

describe("/api/stripe/connect", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET", () => {
    it("should return 401 if user is not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/stripe/connect");
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();

      expect(data.error).toBe("Unauthorized");
    });

    it("should return account status successfully", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      const mockAccountStatus = {
        account: {
          id: "acct_123",
          userId: "user-123",
          status: "enabled",
          chargesEnabled: true,
          payoutsEnabled: true,
          country: "US",
        },
        stripeAccount: {
          id: "acct_123",
          charges_enabled: true,
          payouts_enabled: true,
          details_submitted: true,
          requirements: {},
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.getUserStripeAccount).mockResolvedValue(
        mockAccountStatus,
      );

      const request = new NextRequest("http://localhost/api/stripe/connect");
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();

      expect(data.account).toEqual(mockAccountStatus.account);
      expect(data.stripeAccount.id).toBe("acct_123");
    });

    it("should handle PaymentService errors", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.getUserStripeAccount).mockRejectedValue(
        new Error("Account not found"),
      );

      const request = new NextRequest("http://localhost/api/stripe/connect");
      const response = await GET(request);

      expect(response.status).toBe(500);
    });
  });

  describe("POST", () => {
    it("should return 401 if user is not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost/api/stripe/connect", {
        method: "POST",
        body: JSON.stringify({ country: "US" }),
      });
      const response = await POST(request);

      expect(response.status).toBe(401);
    });

    it("should create Stripe Connect account successfully", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      const mockAccount = {
        id: "acct_123",
        userId: "user-123",
        status: "pending",
        chargesEnabled: false,
        payoutsEnabled: false,
        country: "US",
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.createStripeAccount).mockResolvedValue(
        mockAccount,
      );

      const request = new NextRequest("http://localhost/api/stripe/connect", {
        method: "POST",
        body: JSON.stringify({ country: "US", businessType: "individual" }),
      });
      const response = await POST(request);

      expect(response.status).toBe(201);
      const data = await response.json();

      expect(data.message).toBe("Stripe Connect account created successfully");
      expect(data.account).toEqual(mockAccount);

      expect(PaymentService.createStripeAccount).toHaveBeenCalledWith({
        userId: "user-123",
        email: "<EMAIL>",
        country: "US",
        businessType: "individual",
      });
    });

    it("should handle validation errors", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const request = new NextRequest("http://localhost/api/stripe/connect", {
        method: "POST",
        body: JSON.stringify({ country: "INVALID" }), // Invalid country code
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it("should handle PaymentService errors", async () => {
      const mockSession = {
        user: { id: "user-123", email: "<EMAIL>" },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(PaymentService.createStripeAccount).mockRejectedValue(
        new Error("User already has account"),
      );

      const request = new NextRequest("http://localhost/api/stripe/connect", {
        method: "POST",
        body: JSON.stringify({ country: "US" }),
      });
      const response = await POST(request);

      expect(response.status).toBe(500);
    });
  });
});
