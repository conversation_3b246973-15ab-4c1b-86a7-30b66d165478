import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import {
  withValidationAPIErrorHandling,
  withAPIErrorHandling,
} from "@/lib/error-handler";

// Create Stripe Connect account validation
const createAccountSchema = z.object({
  country: z.string().length(2).optional(),
  businessType: z.enum(["individual", "company"]).optional(),
});

// Create onboarding link validation
const onboardingLinkSchema = z.object({
  returnUrl: z.url("Invalid return URL"),
  refreshUrl: z.url("Invalid refresh URL"),
});

async function getConnectAccount(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get Stripe Connect account status
  const accountStatus = await PaymentService.getUserStripeAccount(
    session.user.id,
  );

  return NextResponse.json({
    account: accountStatus.account,
    stripeAccount: {
      id: accountStatus.stripeAccount.id,
      charges_enabled: accountStatus.stripeAccount.charges_enabled,
      payouts_enabled: accountStatus.stripeAccount.payouts_enabled,
      details_submitted: accountStatus.stripeAccount.details_submitted,
      requirements: accountStatus.stripeAccount.requirements,
    },
  });
}

async function createConnectAccountHandler(
  request: NextRequest,
  validatedData: z.infer<typeof createAccountSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { country, businessType } = validatedData;

  // Create Stripe Connect account
  const account = await PaymentService.createStripeAccount({
    userId: session.user.id,
    email: session.user.email,
    country,
    businessType,
  });

  return NextResponse.json(
    {
      message: "Stripe Connect account created successfully",
      account,
    },
    { status: 201 },
  );
}

export const GET = withAPIErrorHandling(getConnectAccount);
export const POST = withValidationAPIErrorHandling(
  createConnectAccountHandler,
  createAccountSchema,
);
