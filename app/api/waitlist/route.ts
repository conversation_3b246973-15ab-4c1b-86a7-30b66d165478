// app/api/waitlist/route.ts

import { NextResponse } from "next/server";
import Airtable from "airtable";

const apiKey = process.env.AIRTABLE_API_KEY;
const baseId = process.env.AIRTABLE_BASE_ID;

if (!apiKey || !baseId) {
  console.error("Airtable config error: API key or Base ID not set");
}

Airtable.configure({ apiKey: apiKey || "" });
const base = baseId ? Airtable.base(baseId) : null;

export async function POST(request: Request) {
  if (!base) {
    return NextResponse.json(
      { error: "Airtable API key or Base ID not configured." },
      { status: 500 },
    );
  }

  let payload: any;

  try {
    payload = await request.json();
  } catch {
    return NextResponse.json(
      { error: "Invalid JSON payload." },
      { status: 400 },
    );
  }

  const { name, email } = payload;

  if (!name || !email) {
    return NextResponse.json(
      { error: "Name and email are required." },
      { status: 400 },
    );
  }

  try {
    await base("Waitlist").create([{ fields: { Name: name, Email: email } }]);

    return NextResponse.json({ success: true }, { status: 200 });
  } catch (err: any) {
    console.error("Airtable error:", err);
    if (err.statusCode === 404) {
      return NextResponse.json(
        {
          error:
            "Airtable base or table not found. Verify Base ID and table name 'Waitlist'.",
        },
        { status: 404 },
      );
    }
    if (err.statusCode === 403) {
      return NextResponse.json(
        {
          error: "Not authorized. Check your Airtable API key and permissions.",
        },
        { status: 403 },
      );
    }

    return NextResponse.json(
      { error: "Failed to add to waitlist. Try again later." },
      { status: 500 },
    );
  }
}
