import { NextRequest, NextResponse } from "next/server";

import { FileService } from "@/lib/services/file";
import { withAPIErrorHandling } from "@/lib/error-handler";

async function uploadFile<PERSON>andler(request: NextRequest): Promise<NextResponse> {
  const formData = await request.formData();
  const file = formData.get("file") as File;

  if (!file) {
    return NextResponse.json({ error: "No file provided" }, { status: 400 });
  }

  // Validate file type and size
  if (!file.type.startsWith("image/")) {
    return NextResponse.json(
      { error: "Only image files are allowed" },
      { status: 400 },
    );
  }

  if (file.size > 5 * 1024 * 1024) {
    // 5MB limit
    return NextResponse.json(
      { error: "File size must be less than 5MB" },
      { status: 400 },
    );
  }

  // Use FileService to upload file
  const result = await FileService.uploadFile(file);

  return NextResponse.json(result);
}

// Apply error handling middleware - authentication handled by middleware
// Note: Using legacy handler since file uploads require special FormData handling
export const POST = withAPIErrorHandling(uploadFileHandler);
