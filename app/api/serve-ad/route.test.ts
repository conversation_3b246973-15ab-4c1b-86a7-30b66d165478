import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { Prisma } from "@prisma/client";

import { POST } from "./route";

import { AdService } from "@/lib/services/ad";

// Mock the AdService
vi.mock("@/lib/services/ad", () => ({
  AdService: {
    serveAd: vi.fn(),
  },
}));

describe("/api/serve-ad", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /api/serve-ad", () => {
    const mockServedAdResult = {
      ad: {
        id: "ad-123",
        name: "Test Ad",
        description: "Test ad description",
        imageUrl: "https://example.com/image.jpg",
        productUrl: "https://example.com/product",
        targetTopics: ["AI", "productivity"],
      },
      appId: "app-123",
      trackingUrl: "https://example.com/track",
      instructions: {
        impression: "POST to /api/impressions with adId and appId",
        click: "POST to /api/impressions with adId, appId, and clicked: true",
      },
    };

    it("should serve an ad with valid request", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
        topics: ["AI", "productivity"],
        userContext: {
          userAgent: "Mozilla/5.0...",
          language: "en-US",
        },
      };

      vi.mocked(AdService.serveAd).mockResolvedValue(mockServedAdResult);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockServedAdResult);
      expect(AdService.serveAd).toHaveBeenCalledWith({
        appId: "app-123",
        appSecret: "secret-456",
        topics: ["AI", "productivity"],
        userContext: {
          userAgent: "Mozilla/5.0...",
          language: "en-US",
        },
      });
    });

    it("should serve an ad with minimal required fields", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
      };

      vi.mocked(AdService.serveAd).mockResolvedValue(mockServedAdResult);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockServedAdResult);
      expect(AdService.serveAd).toHaveBeenCalledWith({
        appId: "app-123",
        appSecret: "secret-456",
        topics: undefined,
        userContext: undefined,
      });
    });

    it("should return 400 for missing appId", async () => {
      const requestBody = {
        appSecret: "secret-456",
      };

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("App ID is required");
      expect(AdService.serveAd).not.toHaveBeenCalled();
    });

    it("should return 400 for missing appSecret", async () => {
      const requestBody = {
        appId: "app-123",
      };

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("App secret is required");
      expect(AdService.serveAd).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid JSON", async () => {
      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: "invalid json",
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid JSON");
      expect(AdService.serveAd).not.toHaveBeenCalled();
    });

    it("should handle topics as array", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
        topics: ["AI", "machine learning", "automation"],
      };

      vi.mocked(AdService.serveAd).mockResolvedValue(mockServedAdResult);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(AdService.serveAd).toHaveBeenCalledWith({
        appId: "app-123",
        appSecret: "secret-456",
        topics: ["AI", "machine learning", "automation"],
        userContext: undefined,
      });
    });

    it("should handle userContext object", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
        userContext: {
          userAgent: "Custom User Agent",
          language: "fr-FR",
        },
      };

      vi.mocked(AdService.serveAd).mockResolvedValue(mockServedAdResult);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(AdService.serveAd).toHaveBeenCalledWith({
        appId: "app-123",
        appSecret: "secret-456",
        topics: undefined,
        userContext: {
          userAgent: "Custom User Agent",
          language: "fr-FR",
        },
      });
    });

    // Error handling tests
    it("should handle InvalidAppCredentialsError from service", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "wrong-secret",
      };
      const { InvalidAppCredentialsError } = await import("@/lib/errors");

      vi.mocked(AdService.serveAd).mockRejectedValue(
        new InvalidAppCredentialsError("Invalid app credentials"),
      );

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Invalid app credentials");
    });

    it("should handle NoAdsAvailableError from service", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
        topics: ["very-specific-topic"],
      };

      const { NoAdsAvailableError } = await import("@/lib/errors");

      vi.mocked(AdService.serveAd).mockRejectedValue(
        new NoAdsAvailableError("No ads available for the specified criteria"),
      );

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("No ads available for the specified criteria");
    });

    it("should handle generic service errors", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
      };

      vi.mocked(AdService.serveAd).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });

    it("should handle Prisma errors", async () => {
      const requestBody = {
        appId: "app-123",
        appSecret: "secret-456",
      };

      const prismaError = new Prisma.PrismaClientKnownRequestError(
        "Unique constraint failed on the fields: (`appId`)",
        {
          code: "P2002",
          clientVersion: "4.0.0",
        },
      );

      vi.mocked(AdService.serveAd).mockRejectedValue(prismaError);

      const request = new NextRequest("http://localhost:3000/api/serve-ad", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toContain(
        "Unique constraint failed on the fields: (`appId`)",
      );
    });
  });
});
