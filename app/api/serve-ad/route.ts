import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { AdService } from "@/lib/services/ad";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Serve ad request validation
const serveAdSchema = z.object({
  appId: z.string({ error: "App ID is required." }),
  appSecret: z.string({ error: "App secret is required." }),
  topics: z.array(z.string()).optional(),
  userContext: z
    .object({
      userAgent: z.string().optional(),
      language: z.string().optional(),
    })
    .optional(),
});

async function serveAdHandler(
  request: NextRequest,
  validatedData: z.infer<typeof serveAdSchema>,
): Promise<NextResponse> {
  const { appId, appSecret, topics, userContext } = validatedData;

  // Use AdService to serve ad
  const result = await AdService.serveAd({
    appId,
    appSecret,
    topics,
    userContext,
  });

  return NextResponse.json(result);
}

// Apply error handling middleware
export const POST = withValidationAPIErrorHandling(
  serveAd<PERSON>and<PERSON>,
  serveAdSchema,
);
