import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/services/payment";
import { withValidationAPIErrorHandling } from "@/lib/error-handler";

// Update budget validation
const updateBudgetSchema = z.object({
  additionalAmount: z
    .number()
    .min(1, "Additional amount must be at least $1")
    .max(100000, "Amount too high"),
});

async function updateBudgetHandler(
  request: NextRequest,
  validatedData: z.infer<typeof updateBudgetSchema>,
  { params }: { params: { id: string } },
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { additionalAmount } = validatedData;
  const advertisementId = params.id;

  // Update advertisement budget
  const result = await PaymentService.updateAdvertisementBudget(
    advertisementId,
    additionalAmount,
  );

  return NextResponse.json({
    message: "Budget updated successfully",
    advertisement: result.advertisement,
    addedAmount: result.addedAmount,
  });
}

export const PATCH = withValidationAPIErrorHandling(
  updateBudgetHandler,
  updateBudgetSchema,
);
