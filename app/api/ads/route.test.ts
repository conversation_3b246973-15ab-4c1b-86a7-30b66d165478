import { describe, it, expect, vi, beforeEach } from "vitest";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { AdStatus, BidType } from "@prisma/client";

import { GET, POST } from "./route";

import { AdService } from "@/lib/services/ad";
import { stringifyDates } from "@/lib/testUtils";

// Mock dependencies
vi.mock("next-auth", () => ({
  getServerSession: vi.fn(),
}));

vi.mock("@/lib/services/ad", () => ({
  AdService: {
    getUserAds: vi.fn(),
    createAd: vi.fn(),
  },
}));

describe("/api/ads", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("GET /api/ads", () => {
    const mockAds = [
      {
        id: "ad-1",
        name: "Test Ad 1",
        description: "Test description 1",
        productUrl: "https://example.com/product1",
        imageUrl: "https://example.com/image1.jpg",
        targetTopics: ["AI", "productivity"],
        budget: 1000,
        bidType: BidType.CPC,
        bidAmount: 0.5,
        status: AdStatus.ACTIVE,
        userId: "user-123",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
        impressions: 100,
        clicks: 5,
        spend: 25,
        ctr: 5,
      },
      {
        id: "ad-2",
        name: "Test Ad 2",
        description: "Test description 2",
        productUrl: "https://example.com/product2",
        imageUrl: null,
        targetTopics: ["machine learning"],
        budget: 500,
        bidType: BidType.CPM,
        bidAmount: 2.0,
        status: AdStatus.PAUSED,
        userId: "user-123",
        createdAt: new Date("2024-01-02"),
        updatedAt: new Date("2024-01-02"),
        impressions: 50,
        clicks: 2,
        spend: 10,
        ctr: 4,
      },
    ];

    it("should return user ads when authenticated", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AdService.getUserAds).mockResolvedValue(mockAds);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({ ads: stringifyDates(mockAds) });
      expect(AdService.getUserAds).toHaveBeenCalledWith("user-123");
    });

    it("should return 401 when not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
      expect(AdService.getUserAds).not.toHaveBeenCalled();
    });

    it("should handle service errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AdService.getUserAds).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "GET",
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });
  });

  describe("POST /api/ads", () => {
    const mockCreatedAd = {
      id: "ad-123",
      name: "New Test Ad",
      description: "New test description",
      productUrl: "https://example.com/new-product",
      imageUrl: "https://example.com/new-image.jpg",
      targetTopics: ["AI", "automation"],
      budget: 2000,
      bidType: BidType.CPC,
      bidAmount: 1.0,
      status: AdStatus.ACTIVE,
      userId: "user-123",
      createdAt: new Date("2024-01-03"),
      updatedAt: new Date("2024-01-03"),
      impressions: 0,
      clicks: 0,
      spend: 0,
      ctr: 0,
    };

    it("should create an ad with valid data", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      const requestBody = {
        name: "New Test Ad",
        description: "New test description",
        productUrl: "https://example.com/new-product",
        imageUrl: "https://example.com/new-image.jpg",
        targetTopics: ["AI", "automation"],
        budget: 2000,
        bidType: BidType.CPC,
        bidAmount: 1.0,
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AdService.createAd).mockResolvedValue(mockCreatedAd);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        message: "Advertisement created successfully",
        ad: stringifyDates(mockCreatedAd),
      });
      expect(AdService.createAd).toHaveBeenCalledWith({
        userId: "user-123",
        name: "New Test Ad",
        description: "New test description",
        productUrl: "https://example.com/new-product",
        imageUrl: "https://example.com/new-image.jpg",
        targetTopics: ["AI", "automation"],
        budget: 2000,
        bidType: "CPC",
        bidAmount: 1.0,
      });
    });

    it("should create an ad without optional imageUrl", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      const requestBody = {
        name: "New Test Ad",
        description: "New test description",
        productUrl: "https://example.com/new-product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPM",
        bidAmount: 2.5,
      };

      const mockCreatedAdNoImage = { ...mockCreatedAd, imageUrl: null };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AdService.createAd).mockResolvedValue(mockCreatedAdNoImage);

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(AdService.createAd).toHaveBeenCalledWith({
        userId: "user-123",
        name: "New Test Ad",
        description: "New test description",
        productUrl: "https://example.com/new-product",
        imageUrl: undefined,
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPM",
        bidAmount: 2.5,
      });
    });

    it("should return 401 when not authenticated", async () => {
      vi.mocked(getServerSession).mockResolvedValue(null);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Unauthorized");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    // Validation tests
    it("should return 400 for missing required fields", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        // Missing name, description, productUrl, targetTopics, budget, bidType, bidAmount
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Campaign name is required");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    it("should return 400 for budget too low", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 0.5, // Below minimum of 1
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Budget must be at least $1");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    it("should return 400 for budget too high", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 150000, // Above maximum of 100000
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Budget too high");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid bidType", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "INVALID_TYPE",
        bidAmount: 0.5,
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain(
        'Invalid option: expected one of "CPC"|"CPM"',
      );
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    it("should return 400 for bidAmount too low", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPC",
        bidAmount: 0.005, // Below minimum of 0.01
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Bid amount must be at least $0.01");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    it("should return 400 for too many target topics", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: Array(15).fill("topic"), // More than maximum of 10
        budget: 1000,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Too many target topics");
      expect(AdService.createAd).not.toHaveBeenCalled();
    });

    // Service error handling tests
    it("should handle AuthenticationError from service", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);

      const { AuthenticationError } = await import("@/lib/errors");

      vi.mocked(AdService.createAd).mockRejectedValue(
        new AuthenticationError("User must have ADVERTISER role to create ads"),
      );

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("User must have ADVERTISER role to create ads");
    });

    it("should handle generic service errors", async () => {
      const mockSession = {
        user: {
          id: "user-123",
          email: "<EMAIL>",
          roles: ["ADVERTISER"],
        },
      };

      const requestBody = {
        name: "Test Ad",
        description: "Test description",
        productUrl: "https://example.com/product",
        targetTopics: ["AI"],
        budget: 1000,
        bidType: "CPC",
        bidAmount: 0.5,
      };

      vi.mocked(getServerSession).mockResolvedValue(mockSession);
      vi.mocked(AdService.createAd).mockRejectedValue(
        new Error("Database connection failed"),
      );

      const request = new NextRequest("http://localhost:3000/api/ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Database connection failed");
    });
  });
});
