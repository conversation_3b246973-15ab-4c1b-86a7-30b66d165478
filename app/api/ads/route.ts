import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { AdService } from "@/lib/services/ad";
import {
  withValidationAPIErrorHandling,
  withAPIErrorHandling,
} from "@/lib/error-handler";

// Advertisement creation validation
const advertisementSchema = z.object({
  name: z
    .string({ error: "Campaign name is required" })
    .max(100, "Campaign name too long"),
  description: z
    .string({ error: "Description is required" })
    .max(500, "Description too long"),
  productUrl: z.url({ error: "Invalid product URL" }),
  imageUrl: z.url({ error: "Invalid image URL" }).optional().nullable(),
  targetTopics: z.array(z.string()).max(10, "Too many target topics"),
  budget: z
    .number()
    .min(1, "Budget must be at least $1")
    .max(100000, "Budget too high"),
  bidType: z.enum(["CPC", "CPM"]),
  bidAmount: z
    .number()
    .min(0.01, "Bid amount must be at least $0.01")
    .max(100, "Bid amount too high"),
});

async function getAds(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  // Middleware handles authentication and role checking, but we still need session for user ID
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Use AdService to get user ads
  const ads = await AdService.getUserAds(session.user.id);

  return NextResponse.json({ ads });
}

export const GET = withAPIErrorHandling(getAds);

async function createAdHandler(
  request: NextRequest,
  validatedData: z.infer<typeof advertisementSchema>,
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  // Middleware handles authentication and role checking, but we still need session for user ID
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const {
    name,
    description,
    productUrl,
    imageUrl,
    targetTopics,
    budget,
    bidType,
    bidAmount,
  } = validatedData;

  // Use AdService to create ad
  const ad = await AdService.createAd({
    userId: session.user.id,
    name,
    description,
    productUrl,
    imageUrl: imageUrl || undefined,
    targetTopics,
    budget,
    bidType,
    bidAmount,
  });

  return NextResponse.json(
    {
      message: "Advertisement created successfully",
      ad,
    },
    { status: 201 },
  );
}

// Apply error handling middleware - authentication handled by middleware
export const POST = withValidationAPIErrorHandling(
  createAdHandler,
  advertisementSchema,
);
