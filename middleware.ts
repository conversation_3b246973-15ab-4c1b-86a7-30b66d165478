import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

import {
  checkRateLimit,
  rateLimitConfigs,
  RateLimitConfig,
} from "@/lib/rateLimit";

type Middleware = (
  req: NextRequest,
) => NextResponse | Promise<NextResponse | void> | void;

function composeMiddleware(...middlewares: Middleware[]): Middleware {
  return async (req: NextRequest) => {
    for (const mw of middlewares) {
      const res = await mw(req);

      if (res instanceof NextResponse) return res;
    }

    return NextResponse.next();
  };
}

function createAuthMiddleware(
  requiredRoles?: string[],
  requireEmailVerification = true,
): Middleware {
  return async (req: NextRequest) => {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    if (requireEmailVerification && !token.emailVerified) {
      return NextResponse.json(
        { error: "Email verification required" },
        { status: 403 },
      );
    }

    if (requiredRoles?.length) {
      const userRoles = (token.roles as string[]) || [];
      const hasRequiredRole = requiredRoles.some((role) =>
        userRoles.includes(role),
      );

      if (!hasRequiredRole) {
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403 },
        );
      }
    }
  };
}

function createRateLimitMiddleware(config: RateLimitConfig): Middleware {
  return async (req: NextRequest) => {
    const forwarded = req.headers.get("x-forwarded-for");
    const ip = forwarded
      ? forwarded.split(",")[0]
      : req.headers.get("x-real-ip") || "unknown";
    const userAgent = req.headers.get("user-agent") || "unknown";
    const identifier = `${ip}:${userAgent.slice(0, 50)}`;

    const { allowed, remaining, resetTime } = checkRateLimit(
      identifier,
      config,
    );

    if (!allowed) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          retryAfter: Math.ceil((resetTime - Date.now()) / 1000),
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": config.maxRequests.toString(),
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": Math.ceil(resetTime / 1000).toString(),
            "Retry-After": Math.ceil(
              (resetTime - Date.now()) / 1000,
            ).toString(),
          },
        },
      );
    }

    const response = NextResponse.next();

    response.headers.set("X-RateLimit-Limit", config.maxRequests.toString());
    response.headers.set("X-RateLimit-Remaining", remaining.toString());
    response.headers.set(
      "X-RateLimit-Reset",
      Math.ceil(resetTime / 1000).toString(),
    );

    return response;
  };
}

function createCorsMiddleware(
  allowedOrigins = ["http://localhost:3000"],
): Middleware {
  return async (req: NextRequest) => {
    const origin = req.headers.get("origin");
    const corsHeaders = {
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    };

    if (req.method === "OPTIONS") {
      const response = new NextResponse(null, { status: 200 });

      Object.entries(corsHeaders).forEach(([key, value]) =>
        response.headers.set(key, value),
      );
      if (
        origin &&
        (allowedOrigins.includes("*") || allowedOrigins.includes(origin))
      ) {
        response.headers.set("Access-Control-Allow-Origin", origin);
      }
      response.headers.set("Access-Control-Max-Age", "86400");

      return response;
    }

    const response = NextResponse.next();

    Object.entries(corsHeaders).forEach(([key, value]) =>
      response.headers.set(key, value),
    );
    if (
      origin &&
      (allowedOrigins.includes("*") || allowedOrigins.includes(origin))
    ) {
      response.headers.set("Access-Control-Allow-Origin", origin);
    }

    return response;
  };
}

function createSecurityHeadersMiddleware(): Middleware {
  return async () => {
    const response = NextResponse.next();
    const securityHeaders = {
      "X-Content-Type-Options": "nosniff",
      "X-Frame-Options": "DENY",
      "X-XSS-Protection": "1; mode=block",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "Content-Security-Policy":
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
    };

    Object.entries(securityHeaders).forEach(([key, value]) =>
      response.headers.set(key, value),
    );

    return response;
  };
}

// Middleware compositions
const baseMiddleware = composeMiddleware(createSecurityHeadersMiddleware());
const apiMiddleware = composeMiddleware(
  baseMiddleware,
  createRateLimitMiddleware(rateLimitConfigs.api),
);
const authApiMiddleware = composeMiddleware(
  apiMiddleware,
  createAuthMiddleware(),
);
const advertiserMiddleware = composeMiddleware(
  apiMiddleware,
  createAuthMiddleware(["ADVERTISER"]),
);
const modelProviderMiddleware = composeMiddleware(
  apiMiddleware,
  createAuthMiddleware(["MODEL_PROVIDER"]),
);
const uploadMiddleware = composeMiddleware(
  baseMiddleware,
  createRateLimitMiddleware(rateLimitConfigs.upload),
  createAuthMiddleware(),
);
const serveAdMiddleware = composeMiddleware(
  baseMiddleware,
  createRateLimitMiddleware(rateLimitConfigs.serve),
  createCorsMiddleware(["*"]),
);
const authRegistrationMiddleware = composeMiddleware(
  baseMiddleware,
  createRateLimitMiddleware(rateLimitConfigs.auth),
);

export default function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Route mapping for cleaner organization
  const routes = [
    {
      pattern: /^\/api\/auth\/(register|verify-email|resend-verification)/,
      middleware: authRegistrationMiddleware,
    },
    {
      pattern: /^\/api\/auth\/(update-profile|assign-roles)/,
      middleware: authApiMiddleware,
    },
    {
      pattern: /^\/api\/(ads|analytics\/advertiser|stripe\/payments)/,
      middleware: advertiserMiddleware,
    },
    {
      pattern:
        /^\/api\/(apps|analytics\/model-provider|stripe\/connect|stripe\/payouts)/,
      middleware: modelProviderMiddleware,
    },
    {
      pattern: /^\/api\/stripe\/transactions/,
      middleware: authApiMiddleware, // Both roles can access transactions
    },
    { pattern: /^\/api\/upload/, middleware: uploadMiddleware },
    { pattern: /^\/api\/serve-ad/, middleware: serveAdMiddleware },
    { pattern: /^\/api\//, middleware: apiMiddleware },
  ];

  for (const { pattern, middleware } of routes) {
    if (pattern.test(pathname)) {
      return middleware(req);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/api/(.*)", "/((?!_next/static|_next/image|favicon.ico).*)"],
};
