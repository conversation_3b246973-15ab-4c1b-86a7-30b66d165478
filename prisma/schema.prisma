// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

enum Role {
  MODEL_PROVIDER
  ADVERTISER
}

enum AppStatus {
  ACTIVE
  SUSPENDED
}

enum BidType {
  CPC
  CPM
}

enum AdStatus {
  ACTIVE
  PAUSED
  COMPLETED
}

enum TokenType {
  EMAIL_VERIFICATION
  PASSWORD_RESET
  TWO_FACTOR
}



model User {
  id           String    @id @default(cuid())
  email        String    @unique
  passwordHash String? // Made optional for OAuth users
  roles        Role[]
  emailVerified DateTime?
  name         String? // For OAuth user display name
  image        String? // For OAuth user profile image
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Stripe Connect fields
  stripeCustomerId        String? @unique // For advertisers making payments
  stripeConnectAccountId  String? @unique // For model providers receiving payouts

  // Relations
  apps           App[]
  advertisements Advertisement[]
  accounts       Account[]
  sessions       Session[]
  tokens         UserToken[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model UserToken {
  id         String     @id @default(uuid())
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     String

  tokenHash  String
  type       TokenType
  createdAt  DateTime   @default(now())
  expiresAt  DateTime
  usedAt     DateTime?
  metadata   Json?

  @@unique([tokenHash, type])
  @@map("user_tokens")
}

model App {
  id          String    @id @default(cuid())
  userId      String
  name        String
  appId       String    @unique // Public identifier
  appSecret   String // Private key
  description String?
  status      AppStatus @default(ACTIVE)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  impressions AdImpression[]

  @@map("apps")
}

model Advertisement {
  id           String   @id @default(cuid())
  userId       String
  name         String
  description  String
  imageUrl     String?
  productUrl   String
  targetTopics    String[] // Array of topic tags
  budget          Decimal  @db.Decimal(10, 2)
  remainingBudget Decimal  @db.Decimal(10, 2) // Tracks remaining budget for ad spend
  bidType         BidType
  bidAmount       Decimal  @db.Decimal(10, 4)
  status          AdStatus @default(ACTIVE)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  impressions AdImpression[]

  @@map("advertisements")
}

model AdImpression {
  id        String   @id @default(cuid())
  adId      String
  appId     String
  timestamp DateTime @default(now())
  clicked   Boolean  @default(false)
  ipAddress String?
  userAgent String?

  // Relations
  advertisement Advertisement @relation(fields: [adId], references: [id], onDelete: Cascade)
  app           App           @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@map("ad_impressions")
}
