"use client";

import { useState } from "react";
import { Role } from "@prisma/client";
import { useSession, signOut } from "next-auth/react";
import NextLink from "next/link";
import { Avatar } from "@heroui/avatar";
import { Button } from "@heroui/button";
import { Chip } from "@heroui/chip";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@heroui/dropdown";
import { Divider } from "@heroui/divider";
import clsx from "clsx";

import { getFilteredNavigation, getActiveNavigationItem } from "@/lib/dashboard-navigation";
import { Logo } from "@/components/icons";

interface DashboardSidebarProps {
  userRoles: Role[];
  currentPath: string;
}

export function DashboardSidebar({ userRoles, currentPath }: DashboardSidebarProps) {
  const { data: session } = useSession();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const navigation = getFilteredNavigation(userRoles);
  const activeItem = getActiveNavigationItem(currentPath);
  
  const handleSignOut = () => signOut({ callbackUrl: "/" });

  const sidebarContent = (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-default-200">
        <NextLink href="/" className="flex items-center gap-2 group">
          <Logo className="w-8 h-8 transition-transform group-hover:scale-105 text-primary" />
          <span className="text-lg font-extrabold text-foreground transition-colors group-hover:text-primary">
            Mindify AI
          </span>
        </NextLink>
      </div>

      {/* User Profile Section */}
      {session && (
        <div className="p-6 border-b border-default-200">
          <div className="flex items-center gap-3 mb-3">
            <Avatar
              name={session.user.email?.charAt(0).toUpperCase()}
              size="md"
              color="primary"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground truncate">
                {session.user.name || session.user.email}
              </p>
              <p className="text-xs text-default-500 truncate">
                {session.user.email}
              </p>
            </div>
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  className="text-default-500"
                >
                  ⋮
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Profile actions">
                <DropdownItem key="profile" as={NextLink} href="/dashboard/settings">
                  Profile Settings
                </DropdownItem>
                <DropdownItem key="logout" color="danger" onClick={handleSignOut}>
                  Sign Out
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          
          {/* Role chips */}
          <div className="flex flex-wrap gap-1">
            {userRoles.map((role) => (
              <Chip
                key={role}
                size="sm"
                variant="flat"
                color={role === Role.ADVERTISER ? "primary" : "secondary"}
              >
                {role === Role.ADVERTISER ? "Advertiser" : "Model Provider"}
              </Chip>
            ))}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 p-6 space-y-6">
        {navigation.map((section, sectionIndex) => (
          <div key={sectionIndex}>
            {section.title && (
              <h3 className="text-xs font-semibold text-default-500 uppercase tracking-wider mb-3">
                {section.title}
              </h3>
            )}
            <ul className="space-y-1">
              {section.items.map((item) => {
                const isActive = activeItem?.href === item.href;
                
                return (
                  <li key={item.href}>
                    <NextLink
                      href={item.href}
                      className={clsx(
                        "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "text-default-700 hover:bg-default-100 hover:text-foreground"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span>{item.label}</span>
                    </NextLink>
                  </li>
                );
              })}
            </ul>
            {sectionIndex < navigation.length - 1 && (
              <Divider className="mt-6" />
            )}
          </div>
        ))}
      </nav>
    </div>
  );

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          isIconOnly
          variant="flat"
          onPress={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          ☰
        </Button>
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={clsx(
          "fixed left-0 top-0 z-40 h-screen w-64 bg-background border-r border-default-200 transition-transform duration-300",
          "lg:translate-x-0",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {sidebarContent}
      </aside>
    </>
  );
}
