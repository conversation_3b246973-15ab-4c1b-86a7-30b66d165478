"use client";

import { Card, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Progress } from "@heroui/progress";
import { Divider } from "@heroui/divider";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
}

export function StatCard({ title, value, subtitle, trend, color = "default" }: StatCardProps) {
  return (
    <Card className="w-full">
      <CardBody className="p-6">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm text-default-500 mb-1">{title}</p>
            <p className="text-2xl font-bold text-foreground">{value}</p>
            {subtitle && (
              <p className="text-sm text-default-400 mt-1">{subtitle}</p>
            )}
          </div>
          {trend && (
            <Chip
              size="sm"
              variant="flat"
              color={trend.isPositive ? "success" : "danger"}
            >
              {trend.isPositive ? "+" : ""}{trend.value}%
            </Chip>
          )}
        </div>
      </CardBody>
    </Card>
  );
}

interface ProgressCardProps {
  title: string;
  current: number;
  total: number;
  unit?: string;
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
}

export function ProgressCard({ title, current, total, unit = "", color = "primary" }: ProgressCardProps) {
  const percentage = total > 0 ? (current / total) * 100 : 0;
  
  return (
    <Card className="w-full">
      <CardBody className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-foreground">{title}</p>
            <p className="text-sm text-default-500">
              {current.toLocaleString()}{unit} / {total.toLocaleString()}{unit}
            </p>
          </div>
          <Progress
            value={percentage}
            color={color}
            className="w-full"
            size="sm"
          />
          <p className="text-xs text-default-400">
            {percentage.toFixed(1)}% of budget used
          </p>
        </div>
      </CardBody>
    </Card>
  );
}

interface ActivityCardProps {
  title: string;
  activities: Array<{
    id: string;
    title: string;
    description: string;
    timestamp: Date;
    type: "campaign" | "app" | "payment" | "system";
  }>;
}

export function ActivityCard({ title, activities }: ActivityCardProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "campaign":
        return "📢";
      case "app":
        return "🤖";
      case "payment":
        return "💳";
      case "system":
        return "⚙️";
      default:
        return "📝";
    }
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <h3 className="text-lg font-semibold">{title}</h3>
      </CardHeader>
      <Divider />
      <CardBody className="p-0">
        <div className="space-y-0">
          {activities.length === 0 ? (
            <div className="p-6 text-center text-default-400">
              <p>No recent activity</p>
            </div>
          ) : (
            activities.slice(0, 5).map((activity, index) => (
              <div key={activity.id}>
                <div className="p-4 hover:bg-default-50 transition-colors">
                  <div className="flex items-start gap-3">
                    <span className="text-lg">{getActivityIcon(activity.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground truncate">
                        {activity.title}
                      </p>
                      <p className="text-xs text-default-500 mt-1">
                        {activity.description}
                      </p>
                      <p className="text-xs text-default-400 mt-1">
                        {formatTimestamp(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                </div>
                {index < activities.length - 1 && index < 4 && <Divider />}
              </div>
            ))
          )}
        </div>
      </CardBody>
    </Card>
  );
}

interface QuickActionCardProps {
  title: string;
  description: string;
  actions: Array<{
    label: string;
    href: string;
    color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
  }>;
}

export function QuickActionCard({ title, description, actions }: QuickActionCardProps) {
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-sm text-default-500 mt-1">{description}</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody className="pt-4">
        <div className="flex flex-wrap gap-2">
          {actions.map((action) => (
            <Chip
              key={action.label}
              as="a"
              href={action.href}
              variant="flat"
              color={action.color || "primary"}
              className="cursor-pointer hover:opacity-80 transition-opacity"
            >
              {action.label}
            </Chip>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}
