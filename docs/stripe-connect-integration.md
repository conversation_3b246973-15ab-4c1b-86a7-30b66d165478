# Stripe Connect Integration

This document describes the Stripe Connect integration for the AI Advertisement Platform, which enables payments between advertisers and model providers.

## Overview

The platform uses **Stripe Connect** with the following architecture:
- **Platform Type**: Platforms and marketplaces
- **Account Type**: Express accounts for model providers
- **Onboarding**: Embedded onboarding flow
- **Dashboard**: Express dashboard access for connected accounts
- **Payment Flow**: Destination charges
- **Fee Structure**: Platform pays Stripe fees

## Business Logic

### Payment Flow
1. **Advertisers** deposit funds to their advertising budget via Stripe payment intents
2. **Budget deduction** happens automatically when ads are served (based on CPM/CPC)
3. **Model providers** receive payouts via Stripe transfers to their connected accounts
4. **Platform** facilitates transactions and pays all Stripe fees

### User Roles
- **Advertisers**: Can make payments to fund advertising budgets
- **Model Providers**: Can create Stripe Connect accounts to receive payouts

## Setup Instructions

### 1. Stripe Dashboard Configuration

1. **Create a Stripe Account**
   - Go to [Stripe Dashboard](https://dashboard.stripe.com)
   - Complete your account setup

2. **Enable Stripe Connect**
   - Navigate to Connect → Settings
   - Choose "Platforms and marketplaces"
   - Configure your platform settings

3. **Get API Keys**
   - Go to Developers → API keys
   - Copy your publishable and secret keys
   - Add them to your `.env` file

4. **Configure Webhooks**
   - Go to Developers → Webhooks
   - Create a new endpoint: `https://yourdomain.com/api/webhooks/stripe`
   - Select these events:
     - `account.updated`
     - `payment_intent.succeeded`
     - `payment_intent.payment_failed`
     - `transfer.created`
     - `transfer.failed`
     - `account.application.deauthorized`
   - Copy the webhook secret to your `.env` file

### 2. Environment Variables

Add these to your `.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret_here"
```

### 3. Database Schema

The integration uses a simplified schema that relies on Stripe as the source of truth:

```sql
-- User table includes Stripe reference fields
ALTER TABLE users ADD COLUMN stripe_customer_id VARCHAR UNIQUE;
ALTER TABLE users ADD COLUMN stripe_connect_account_id VARCHAR UNIQUE;

-- Advertisement table includes budget tracking
ALTER TABLE advertisements ADD COLUMN remaining_budget DECIMAL(10,2);
```

## API Endpoints

### Stripe Connect Account Management

#### Create Connect Account
```http
POST /api/stripe/connect
Content-Type: application/json

{
  "country": "US",
  "businessType": "individual"
}
```

#### Get Account Status
```http
GET /api/stripe/connect
```

#### Create Onboarding Link
```http
POST /api/stripe/connect/onboarding
Content-Type: application/json

{
  "returnUrl": "https://yourdomain.com/dashboard/model-provider/payments?onboarding=complete",
  "refreshUrl": "https://yourdomain.com/dashboard/model-provider/payments?onboarding=refresh"
}
```

#### Create Dashboard Link
```http
POST /api/stripe/connect/dashboard
```

### Payment Management

#### Create Payment Intent
```http
POST /api/stripe/payments
Content-Type: application/json

{
  "amount": 100.00,
  "currency": "usd",
  "description": "Budget deposit",
  "metadata": {
    "campaign_id": "camp_123"
  }
}
```

#### Get Payment History
```http
GET /api/stripe/payments
```

### Payout Management

#### Create Payout
```http
POST /api/stripe/payouts
Content-Type: application/json

{
  "amount": 50.00,
  "currency": "usd",
  "description": "Model provider earnings"
}
```

#### Get Payout History
```http
GET /api/stripe/payouts
```

### Transaction History

#### Get All Transactions
```http
GET /api/stripe/transactions
```

## Frontend Components

### For Model Providers

#### Stripe Connect Onboarding
```tsx
import StripeConnectOnboarding from "@/components/payment/stripe-connect-onboarding";

<StripeConnectOnboarding
  account={account}
  onAccountCreated={handleAccountCreated}
  onRefresh={handleRefresh}
/>
```

#### Payout Dashboard
```tsx
import PayoutDashboard from "@/components/payment/payout-dashboard";

<PayoutDashboard
  payouts={payouts}
  transactions={transactions}
  totalEarnings={totalEarnings}
  pendingEarnings={pendingEarnings}
  onRefresh={handleRefresh}
  onOpenDashboard={handleOpenDashboard}
/>
```

### For Advertisers

#### Payment Management
```tsx
import PaymentManagement from "@/components/payment/payment-management";

<PaymentManagement
  payments={payments}
  onPaymentCreated={handlePaymentCreated}
  onRefresh={handleRefresh}
/>
```

## Budget Deduction Logic

Budget deduction happens automatically when ads are served:

1. **CPM (Cost Per Mille)**: Deducts `bidAmount / 1000` for each impression
2. **CPC (Cost Per Click)**: Deducts `bidAmount` only when clicked

The system checks for sufficient budget before serving ads and throws `InsufficientBudgetError` if budget is insufficient.

## Error Handling

The integration uses centralized error handling with custom error classes:

- `PaymentError`: General payment processing errors
- `StripeAccountNotFoundError`: When user has no Stripe Connect account
- `StripeAccountNotEnabledError`: When account is not enabled for payments/payouts
- `InsufficientBudgetError`: When advertiser has insufficient budget
- `InvalidPaymentAmountError`: When payment amount is invalid

## Testing

Run the test suite:

```bash
bun test:run lib/services/payment.test.ts
bun test:run app/api/stripe/
```

## Security Considerations

1. **Webhook Verification**: All webhooks are verified using Stripe signatures
2. **API Authentication**: All endpoints require user authentication
3. **Role-based Access**: Advertisers can only access payment endpoints, model providers can only access Connect endpoints
4. **Data Validation**: All inputs are validated using Zod schemas

## Monitoring and Logging

The integration includes comprehensive logging for:
- Account status changes
- Payment successes and failures
- Transfer successes and failures
- Budget deductions
- Error conditions

Monitor these logs for operational insights and debugging.
